package be.fgov.onerva.person.backend.request.broker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AmqpBrokerService implements BrokerService {

    private final AmqpTemplate amqpTemplate;

    @Override
    public void convertAndSend(Object message) {
        log.debug("Sent : {}", sanitizeLogMessage(message));
        try {
            amqpTemplate.convertAndSend(message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private String sanitizeLogMessage(Object msg) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // convert object to JSON
            String jsonString = objectMapper.writeValueAsString(msg);

            // parse JSON
            JsonNode jsonNode = objectMapper.readTree(jsonString);

            // traverse tree, replace ssin value
            JsonNode alteredJsonNode = traverseAndObfuscate(jsonNode);

            // convert back to JSON string for logging
            return objectMapper.writeValueAsString(alteredJsonNode);

        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }

        return msg.toString();
    }

    private JsonNode traverseAndObfuscate(JsonNode node) {
        if (node.isObject()) {
            Iterator<Map.Entry<String,JsonNode>> iter = node.fields();
            while (iter.hasNext()) {
                Map.Entry<String,JsonNode> entry = iter.next();
                if(entry.getKey().toLowerCase().equals("ssin")) {
                    ((ObjectNode) node).put(entry.getKey(), "REDACTED");
                } else {
                    traverseAndObfuscate(entry.getValue());
                }
            }
        } else if (node.isArray()) {
            for (JsonNode arrayItem : node) {
                traverseAndObfuscate(arrayItem);
            }
        }

        return node;
    }
}
