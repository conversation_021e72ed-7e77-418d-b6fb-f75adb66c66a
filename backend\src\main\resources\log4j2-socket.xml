<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
<Appenders>

    <Socket name="elastic" host="monitoring-fluent-bit.centralized-logging.svc.cluster.local" port="5170">
        <JsonTemplateLayout eventTemplateUri="classpath:EcsLayout.json">
            <EventTemplateAdditionalField key="application" value="person"/>
            <EventTemplateAdditionalField key="domain" value="apps-kdd"/>
            <EventTemplateAdditionalField key="environment" value="${env:environment}"/>
            <EventTemplateAdditionalField key="cloud-instance-id" value="${env:HOSTNAME}"/>
        </JsonTemplateLayout>
    </Socket>

    <Async name="async">
        <AppenderRef ref="elastic"/>
    </Async>

</Appenders>

    <Loggers>

        <!-- LOG everything at INFO level and above -->
        <Root level="info">
            <AppenderRef ref="async"/>
        </Root>

    </Loggers>
</Configuration>
