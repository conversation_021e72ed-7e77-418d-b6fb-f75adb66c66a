# 3. error management in person creation

Date: 2023-09-12

## Status

Accepted

## Context

With adr 0002 we validate a flow to create a new person in the mainframe. As the communication with the mainframe is 
asynchronous, we need to setup an error management.

## Decision

Normally no functional error are expected because data quality is validated by the person service. We expect to receive 
two kinds of error:
    
1) Unable to publish to IN queue, person service will implement a retry functionality to try to re-push in the IN queue.
2) Mainframe can return technical error in the OUT queue. In that case an email / or an alert will be sent to warn that 
an error appears

## Consequences

We don't expect to have so many errors, so it's why we choose to have this manual error management.