
CREATE DATABASE db1;

USE db1;
create table keybox_ds
(
    num_pens          decimal(10) not null
        constraint keybox_access
            primary key
                with (fillfactor = 50),
    nom_prenom        char(30),
    phoneme_nom       char(6),
    phoneme_prenom    char(4),
    num_box           int,
    sexe_clair        smallint,
    date_naiss        int,
    code_post         int,
    last_id           smallint,
    num_br            smallint,
    flag_id           bit         ,
    flag_ok           bit         ,
    flag_to_purge     bit         ,
    flag_purge        bit         ,
    flag_personnel    bit         ,
    source_id         tinyint     ,
    my_id             int identity
)

create unique index keybox_ds_tk
    on keybox_ds (my_id)
    with (fillfactor = 50)

create index keybox_ds_fk
    on keybox_ds (num_pens)
    with (fillfactor = 50)

create index Keybox_ds_NumBox
    on keybox_ds (num_box, last_id)
    with (fillfactor = 50)

create index NC__keybox_ds__last_id__Include
    on keybox_ds (last_id) include (num_pens, num_box, nom_prenom, sexe_clair, code_post, num_br)

create index NC__keybox_ds__code_post__Include
    on keybox_ds (code_post) include (num_pens, num_br)
    with (fillfactor = 50)

create index NC__keybox_ds__last_id__num_box__Include
    on keybox_ds (last_id, num_box) include (num_pens, nom_prenom)
    with (fillfactor = 50)

create index NC__keybox_ds__num_br__num_pens
    on keybox_ds (num_br, num_pens)
    with (fillfactor = 50)

create table general_ds (
    id                  bigint not null constraint general_ds_pk primary key,
    nom_prenom_gn       char(30),
    adresse             char(30),
    num_box             int,
    code_post           int,
    code_resid          int,
    langue_gn           smallint,
    mode_pay_gn         smallint,
    sexe_gn             bit not null,
    flag_nation         bit not null,
    sect_op             int,
    mandat_syndic       char(1),
    num_br              smallint,
    cpte_iban_gn        char(34),
    cpte_bic_gn         char(11),
    cpte_tit_gn         char(20),
    date_naiss_gn       int,
    date_deces_car_gn   int,
    flag_v_cpte         bit not null,
    date_v_cpte         int,
    date_m_cpte         int,
    date_v_siggen       int,
    flag_v_siggen       bit not null
)

create index NC__dbo_general_ds__nom_prenom_gn__num_box
    on general_ds (nom_prenom_gn, num_box)


create table communic_ds
(
    num_box            int      not null,
    date_valid         int      not null,
    flag_valid         smallint not null,
    date_modif         int,
    code_operateur     int,
    date_modif_f4      int,
    code_operateur_f4  int,
    tel_reg            char(17),
    gsm_reg            char(17),
    email_reg          char(50),
    tel_onem           char(17),
    gsm_onem           char(17),
    email_onem         char(50),
    date_modif_reg     int,
    date_modif_onem    int,
    e_box              char,
    date_modif_e_box   int,
    e_box_active       char,
    paperless          char,
    email_onem_2       char(50),
    source_id          tinyint  not null,
    tel_reg_sanitized  varchar(20),
    gsm_reg_sanitized  varchar(20),
    tel_onem_sanitized varchar(20),
    gsm_onem_sanitized varchar(20),
    id                 bigint identity
        constraint PK_dbo_communic_dsid
            primary key,
    my_id              int,
    my_aa              char(12)
)


create unique index aa_set_communic_ds
    on communic_ds (my_aa)


create index communic_by_box
    on communic_ds (num_box asc, date_valid desc, flag_valid desc, id desc)


create index communic_ds_fk
    on communic_ds (num_box, source_id)


create unique index communic_ds_tk
    on communic_ds (my_id)


create index NC__dbo_communic_ds__flag_valid__gsm_reg_sanitized__Include
    on communic_ds (flag_valid, gsm_reg_sanitized) include (num_box, tel_reg_sanitized, email_reg, tel_onem_sanitized,
                                                            gsm_onem_sanitized, email_onem, my_aa, my_id)


create index NC__dbo_communic_ds__flag_valid__Include
    on communic_ds (flag_valid) include (num_box, tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem)


create index NC__dbo_Communic_ds__flag_valid__num_box__Include
    on communic_ds (flag_valid, num_box) include (my_aa, email_reg, email_onem)


create table dbo.nation_ds
(
    num_box           int      not null,
    code_operateur    int,
    flag_valid        smallint not null,
    date_valid        int      not null,
    date_modif        int,
    nation            smallint,
    date_modif_f4     int,
    code_operateur_f4 int,
    nation_bcss       smallint,
    refugie           smallint,
    source_id         tinyint  not null,
    id                bigint identity
        constraint PK_dbo_nation_dsid
            primary key,
    my_id             int,
    my_aa             char(12)
)


create unique index aa_set_nation_ds
    on dbo.nation_ds (my_aa)


create unique index nation_by_9
    on dbo.nation_ds (flag_valid asc, num_box asc, date_valid desc)



create index nation_by_box
    on dbo.nation_ds (num_box asc, date_valid desc, flag_valid desc, id desc)


create index NC__nation_ds__flag_valid__num_box__date_valid
    on dbo.nation_ds (flag_valid, num_box, date_valid)


create index NC__nation_ds__num_box__flag_valid__date_valid
    on dbo.nation_ds (num_box, flag_valid, date_valid)

create unique index UDX__nation_ds__my_id
    on dbo.nation_ds (my_id)

create table dbo.cpte_ds
(
    cpte_tit          char(20),
    date_valid        int      not null,
    flag_valid        smallint not null,
    date_modif        int,
    date_modif_f4     int,
    code_operateur    int,
    code_operateur_f4 int,
    cpte_num          decimal(11),
    cpte_iban         char(34),
    cpte_bic          char(11),
    source_id         tinyint  not null,
    user_column1      tinyint,
    id                bigint identity
        constraint PK_dbo_cpte_dsid
            primary key,
    my_id             int,
    my_rsn            char(12),
    parent_id         bigint not null
)

create unique index aa_set_cpte_ds
    on dbo.cpte_ds (my_rsn)

create index cpte_access
    on dbo.cpte_ds ( date_valid desc, flag_valid desc, id desc)

create unique index PARENT_cpte_ds
    on dbo.cpte_ds ( id)


create table commune_ds
(
    num_box            int      not null,
    flag_valid         smallint not null,
    date_valid         int      not null,
    adresse            char(30),
    code_post          int,
    code_resid         int      not null,
    id                 bigint identity
        constraint PK_dbo_commune_dsid
            primary key
)

create unique index commune_by_9 on commune_ds (flag_valid ASC, num_box ASC, date_valid DESC);

create index commune_by_box on commune_ds (num_box ASC, date_valid DESC, flag_valid DESC, id DESC);


create table sectop_ds (
    num_box           int      not null,
    date_valid        int      not null,
    flag_valid        smallint not null,
    sect_op           int,
    mandat_syndic     char(1),
    id                bigint identity constraint PK_dbo_sectop_dsid primary key
)

create unique index sectop_by_9 on sectop_ds (flag_valid ASC, num_box ASC, date_valid DESC);

create index sectop_by_box on sectop_ds (num_box asc, date_valid desc, flag_valid desc, id desc);

create unique index sectop_by_box_u on sectop_ds (num_box, id);


create table prof_ds
(
    num_box           int      not null,
    flag_valid        smallint not null,
    date_valid        int      not null,
    id                bigint identity
        constraint PK_dbo_prof_dsid
            primary key
)

create unique index prof_by_9
    on prof_ds (num_box asc, date_valid desc) include (flag_valid)

create index prof_by_9_nu
    on prof_ds (num_box asc, date_valid desc) include (flag_valid)

create table contrat_ds
(
    num_box           int      not null,
    flag_valid        smallint not null,
    date_valid        int      not null,
    contrat_trav      SMALLINT,
    id                bigint identity
        constraint PK_dbo_contrat_dsid
            primary key
)

create index contrat_by_box
    on contrat_ds (num_box asc, date_valid desc, flag_valid desc, id desc)


CREATE TABLE siggen_ds (
    date_valid        INT NOT NULL,
    flag_valid        SMALLINT NOT NULL,
    langue            SMALLINT,
    mode_pay          SMALLINT,
    id                BIGINT NOT NULL,
    parent_id         BIGINT NOT NULL,
    CONSTRAINT PK_dbo_siggen_dsid PRIMARY KEY (id),
    CONSTRAINT FK_dbo_siggen_dsgeneral_ds FOREIGN KEY (parent_id) REFERENCES "general_ds" ("id")
);

create unique index PARENT_siggen_ds on siggen_ds (parent_id, id);

create index siggen_access on siggen_ds (parent_id asc, date_valid desc, flag_valid desc, id desc);

CREATE TABLE commune_etr_ds (
    num_box           INT NOT NULL,
    date_valid        INT NOT NULL,
    flag_valid        SMALLINT NOT NULL,
    rue               CHAR(35),
    numero            CHAR(10),
    code_post         CHAR(10),
    commune           CHAR(35),
    pays              CHAR(20),
    id                BIGINT NOT NULL,
    CONSTRAINT PK_dbo_commune_etr_dsid PRIMARY KEY (id)
);

create unique index commune_etr_by_9 on commune_etr_ds (flag_valid ASC, num_box ASC, date_valid DESC);
