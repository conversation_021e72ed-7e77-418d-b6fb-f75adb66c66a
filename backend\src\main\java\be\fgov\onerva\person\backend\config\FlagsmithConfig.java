package be.fgov.onerva.person.backend.config;

import com.flagsmith.FlagsmithClient;
import com.flagsmith.config.FlagsmithCacheConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class FlagsmithConfig {

    @Value("${flagsmith.environment.id}")
    private String flagsmithEnvironmentId;
    @Value("${flagsmith.api}")
    private String flagsmithApiUrl;

    @Bean
    public FlagsmithClient flags() {
        return FlagsmithClient.newBuilder()
                .withApiUrl(flagsmithApiUrl)
                .setApiKey(flagsmithEnvironmentId)
                .enableLogging()
                .withCache(
                        FlagsmithCacheConfig
                        .newBuilder()
                        .expireAfterAccess(1, TimeUnit.MINUTES)
                        .build()
                )
                .build();
    }

}
