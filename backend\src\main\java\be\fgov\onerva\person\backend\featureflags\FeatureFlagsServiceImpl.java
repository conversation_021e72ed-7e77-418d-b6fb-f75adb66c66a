package be.fgov.onerva.person.backend.featureflags;

import com.flagsmith.FlagsmithClient;
import com.flagsmith.exceptions.FlagsmithClientError;
import com.flagsmith.models.BaseFlag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeatureFlagsServiceImpl implements FeatureFlagsService {

    private final FlagsmithClient flagsmithClient;

    @Override
    public boolean isFeatureEnabled(String featureFlag) {
        log.debug(String.format("Check if feature %s is enabled", featureFlag));
        try {
            var isEnabled = flagsmithClient.getEnvironmentFlags().isFeatureEnabled(featureFlag);
            log.debug(String.format("Feature %s is enabled ? %s", featureFlag, isEnabled));
            return isEnabled;
        } catch (FlagsmithClientError e) {
            log.error("Error while getting the feature flag '{}'. It will default to false.", featureFlag,  e);
            return false;
        }
    }

    @Override
    public BaseFlag getFlag(String feature) {
        log.debug(String.format("Get the flag for feature %s", feature));
        try {
            var flag = flagsmithClient.getEnvironmentFlags().getFlag(feature);
            log.debug(String.format("Feature %s has flag %s", feature, flag));
            return flag;
        } catch (FlagsmithClientError e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
