asyncapi: 3.0.0
info:
  title: Person Events
  version: 1.0.0
  description: The messages published by the Person service
channels:
  person:
    address: person
    messages:
      PersonCreatedCloudEvent:
        $ref: '#/components/messages/PersonCreatedCloudEvent'
      PersonUpdatedCloudEvent:
        $ref: '#/components/messages/PersonUpdatedCloudEvent'
operations:
  onPersonCreated:
    action: send
    channel:
      $ref: '#/channels/person'
    messages:
      - $ref: '#/channels/person/messages/PersonCreatedCloudEvent'
  onPersonUpdated:
    action: send
    channel:
      $ref: '#/channels/person'
    messages:
      - $ref: '#/channels/person/messages/PersonUpdatedCloudEvent'
components:
  messages:
    PersonCreatedCloudEvent:
      title: A cloud event wrapping a person created message
      summary: >-
        Notifies when a citizen has been created or not (in the mainframe for
        the moment)
      contentType: application/cloudevents+json
      payload:
        $ref: '#/components/schemas/PersonCreatedPayload'
    PersonUpdatedCloudEvent:
      title: A cloud event wrapping a person updated message
      summary: >-
        Notifies when a citizen has been updated or not
      contentType: application/cloudevents+json
      payload:
        $ref: '#/components/schemas/PersonUpdatedPayload'
  schemas:
    PersonCreatedPayload:
      type: object
      allOf:
        - $ref: 'https://api-registry.prod.paas.onemrva.priv/apis/registry/v2/groups/be.fgov.onerva.common/artifacts/cloudEvent'
      properties:
        data:
          $ref: '#/components/schemas/PersonCreated'
    PersonUpdatedPayload:
      type: object
      allOf:
        - $ref: 'https://api-registry.prod.paas.onemrva.priv/apis/registry/v2/groups/be.fgov.onerva.common/artifacts/cloudEvent'
      properties:
        data:
          $ref: '#/components/schemas/PersonUpdated'
    PersonResponse:
      type: object
      description: The response of a person request.
      required:
        - id
        - ssin
        - success
      properties:
        id:
          description: Person Request Id
          type: integer
          format: int64
        correlationId:
          description: Optional given correlation id when request was made
          type: string
          maxLength: 50
        ssin:
          description: Citizen's SSIN
          type: string
          pattern: '^\d{11}$'
          example: '79032429187'
        success:
          type: boolean
          description: If the action on the citizen was successful
        names:
          type: string
          description: The last- and firstname formatted stored in the mainframe
    PersonCreated:
      type: object
      allOf:
        - $ref: '#/components/schemas/PersonResponse'
      description: The response of the person creation request.
      required:
        - firstname
        - lastname
        - status
      properties:
        firstname:
          type: string
          example: René
        lastname:
          type: string
          example: Artois
        status:
          $ref: '#/components/schemas/PersonCreatedStatus'
    PersonCreatedStatus:
      type: string
      description: The status of the person creation request
      enum:
        - CREATED
        - EXISTS
        - INVALID_SSIN
        - ERROR
    PersonUpdated:
      type: object
      allOf:
        - $ref: '#/components/schemas/PersonResponse'
      description: The response of the person update request.
