package be.fgov.onerva.person.backend.lookup.model;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDate;

@SuperBuilder
@Getter @ToString
@Jacksonized
public class LookupData {
    private int id;
    private String code;
    private String descFr;
    private String descNl;
    private LocalDate beginDate;
    private LocalDate endDate;
}

