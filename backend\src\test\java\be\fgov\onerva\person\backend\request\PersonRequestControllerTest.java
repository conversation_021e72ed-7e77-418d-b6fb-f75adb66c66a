package be.fgov.onerva.person.backend.request;

import backend.rest.model.CitizenRequestDTO;
import backend.rest.model.CitizenRequestPageDTO;
import be.fgov.onerva.person.backend.IntegrationTest;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static backend.rest.model.CitizenRequestDTO.TypeEnum.CREATE;
import static be.fgov.onerva.person.backend.request.mapper.PersonRequestMapper.map;

@Transactional
class PersonRequestControllerTest extends IntegrationTest {

    public static final String PATH = "/api/citizen/requests";

    @Autowired
    private PersonRequestRepository repository;

    private LocalDateTime now = LocalDateTime.now().withNano(0);

    private PersonRequest _jane = PersonRequest.builder()
            .type(PersonRequestType.CREATE)
            .niss("70010100287")
            .firstname("Jane")
            .lastname("Doe")
            .created(now)
            .build();

    private PersonRequest _john = PersonRequest.builder()
            .type(PersonRequestType.CREATE)
            .niss("70010100188")
            .firstname("John")
            .lastname("Doe")
            .created(now)
            .correlationId("666")
            .build();

    @AfterEach
    void tearDown() {
        repository.deleteAll();
    }

    @Test
    void getByIdNotFound() {
        rest.get()
                .uri(PATH + "/404")
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.NOT_FOUND);
    }

    @Test
    void getById() {
        var personRequest = repository.saveAndFlush(_jane);

        rest.get()
                .uri(PATH + '/' + personRequest.getId())
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenRequestDTO.class)
                .isEqualTo(new CitizenRequestDTO()
                        .type(CREATE)
                        .niss("70010100287")
                        .id(personRequest.getId())
                        .firstname("Jane")
                        .lastname("Doe")
                        .created(map(personRequest.getCreated()))
                        .sent(false)
                        .retryCount(0)
                )
        ;
    }

    @Test
    void searchCitizenRequests() {
        var john = repository.saveAndFlush(_john);
        var jane = repository.saveAndFlush(_jane);

        rest.get()
                .uri(PATH + "?pageNumber=0&pageSize=5")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenRequestPageDTO.class)
                .isEqualTo(new CitizenRequestPageDTO()
                        .isFirst(true)
                        .isLast(true)
                        .pageSize(5)
                        .pageNumber(0)
                        .totalElements(2)
                        .totalPage(1)
                        .addContentItem(new CitizenRequestDTO()
                                .type(CREATE)
                                .niss("70010100188")
                                .id(john.getId())
                                .firstname("John")
                                .lastname("Doe")
                                .created(map(john.getCreated()))
                                .correlationId("666")
                                .sent(false)
                                .retryCount(0)
                        )
                        .addContentItem(new CitizenRequestDTO()
                                .type(CREATE)
                                .niss("70010100287")
                                .id(jane.getId())
                                .firstname("Jane")
                                .lastname("Doe")
                                .created(map(jane.getCreated()))
                                .sent(false)
                                .retryCount(0)
                        )
                )
        ;
    }
}