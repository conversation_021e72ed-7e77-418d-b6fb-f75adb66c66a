global:
  environment: test
  registry: docker-alpha.onemrva.priv
  routes:
    host: person.test.paas.onemrva.priv

backend:
  springConfiguration:
    spring:
      security:
        oauth2:
          resource-server:
            jwt:
              issuer-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents
          client:
            provider:
              keycloak:
                issuer-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local

    wave:
      api: https://wo-configurator.test.paas.onemrva.priv/api

    datasource:
      mfx:
        url: ****************************************************************************************************************;
        username: CORSEA_Person_user
      person:
        url: *****************************************************************************************************************************************************
        username: person_user

    ibm:
      mq:
        connName: crosstest.onemrva.priv(1606)
        user: MQJAVADEV@onemrva
        queueManager: QM_WEB_DEV
        channel: CHSRVC_WEB_UBENEFIT
        useAuthenticationMQCSP: false

    rabbitmq:
      oauth:
        enabled: true
        clientId: person-backend
        tokenEndpointUri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token

kcconfig:
  keycloak:
    url: https://keycloak.test.paas.onemrva.priv/
