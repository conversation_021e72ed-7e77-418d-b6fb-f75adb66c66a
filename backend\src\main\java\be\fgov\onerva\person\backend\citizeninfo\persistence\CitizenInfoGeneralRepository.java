package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoGeneral;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;


import java.util.List;

public interface CitizenInfoGeneralRepository extends JpaRepository<CitizenInfoGeneral, String>, JpaSpecificationExecutor<CitizenInfoGeneral> {

    List<CitizenInfoGeneral> getCitizenInfoGeneralByNumBoxIn(List<Integer> numbox);
}
