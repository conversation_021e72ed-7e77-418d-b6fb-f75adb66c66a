package be.fgov.onerva.person.backend.citizeninfo.mapper;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class AddressUtilsTest {

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
    # input,                    street,             rest
    NULL,                       NULL,               NULL
    '',                         '',                 NULL
    ' ARISDONK 109  ',          ARISDONK,           109
    'AV. G. ABELOOS,  22/11  ', 'AV. G. ABELOOS',   22/11
    LENNEKE MARELAAN 40 BTE 27, <PERSON>ENNE<PERSON> MARELAAN,   40 BTE 27
    11 NOVEMBERLAAN 10,         11 NOVEMB<PERSON><PERSON><PERSON>,    10
    """)
    void splitStreet(String input, String part1, String part2) {
        String[] parts = AddressUtils.splitStreet(input);
        assertThat(parts[0]).isEqualTo(part1);
        assertThat(parts[1]).isEqualTo(part2);
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
    # input,        number,     box
    NULL,           NULL,       NULL
    '',             '',         NULL
    '109',          109,        NULL
    1 A,            1 A,        NULL
    88C,            88C,        NULL
    '  22/11  ',    22,         11
    40 BTE 27,      40,         27
    4 <PERSON>US 14,       4,          14
    114 <PERSON>US <PERSON>,      114,        <PERSON>
    7BIS,           7BI<PERSON>,       NULL
    13 * 2,         13,         2
    3/M,            3,          <PERSON>
    11/<PERSON>9,          11,         9
    1157 <PERSON>2,        1157,       2
    16 <PERSON> 13,        16,         13
    2 APP <PERSON> 2,      2 APP <PERSON> 2,  NULL
    25 BT4,         25,         4
    392B807,        392,        807
    29/B,           29,         B
    107 AP 2,       107 AP 2,   NULL
    123 1ET,        123 1ET,    NULL
    2A/8,           2A,         8
    7 BUS 1-1,      7,          1-1
    4 BUS 5/6,      4,          5/6
    34 BT9/6,       34,         9/6
    49/BTE.06,      49,         .06
    14B BUS32,      14B,        32
    1 X 42,         1,          42
    3 X,            3 X,        NULL
    '12APP 3 ',     12APP 3,    NULL
    219/APP5,       219/APP5,   NULL
    31- 1I2,        31- 1I2,    NULL
    28B.,           28B.,       NULL
    '2 7IA  ',      2 7IA,      NULL
    '58,6A',        '58,6A',    NULL
    17BAJOS1Y2,     17BAJOS1Y2, NULL
    57B/5,          57B,        5
    25/A20,         25,         A20
    """)
    void splitHouseNumberAndBox(String input, String number, String box) {
        String[] parts = AddressUtils.splitHouseNumberAndBox(input);
        assertThat(parts[0]).isEqualTo(number);
        assertThat(parts[1]).isEqualTo(box);
    }
}