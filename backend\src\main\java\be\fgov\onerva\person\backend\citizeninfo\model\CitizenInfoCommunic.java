package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "communic_ds", schema = "dbo")
public class CitizenInfoCommunic {

    @Id
    private String id;

    private int numBox;

    @Column(name="gsm_onem_sanitized")
    private String gsmOnem;

    @Column(name="tel_onem_sanitized")
    private String telephoneOnem;

    @Column(name="gsm_reg_sanitized")
    private String gsmReg;

    @Column(name="tel_reg_sanitized")
    private String telephoneReg;

    @Column(name="email_onem")
    private String email;

    private String emailReg;

    private int flagValid;

    private int dateValid;
}
