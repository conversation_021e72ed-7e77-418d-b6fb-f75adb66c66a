package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.request.broker.AmqpBrokerService;
import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.person.msg.v1.PersonCreated;
import be.fgov.onerva.person.msg.v1.PersonCreatedPayload;
import be.fgov.onerva.person.msg.v1.PersonCreatedStatus;
import jakarta.jms.JMSException;
import jakarta.jms.TextMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.support.destination.DestinationResolutionException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PersonRequestListenerTest {
    @Mock
    private JmsTemplate jmsTemplate;
    @Mock
    private PersonRequestRepository repository;
    @Mock
    private RestTemplate http;
    @Mock
    private AmqpTemplate amqpTemplate;
    private PersonRequestListener listener;
    @Captor
    ArgumentCaptor<LocalDateTime> captor;

    @Captor
    ArgumentCaptor<PersonCreatedPayload> personCreatedPayloadArgumentCaptor;

    @Mock
    private CitizenRepository citizenRepository;

    @Mock
    private MainframeUpdateMessageFormatter messageFormatter;

    @BeforeEach
    void setup() {
        listener = new PersonRequestListener(jmsTemplate, repository, citizenRepository,
                new AmqpBrokerService(amqpTemplate), messageFormatter);
    }

    @Test
    void handlePersonRequestCreatedEvent() {
        var personRequest = PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .id(123)
                .niss("70010100287")
                .sent(false)
                .retryCount(0)
                .firstname(" Jâne ")
                .lastname(" Doé ")
                .created(LocalDateTime.now())
                .build();
        var event = new PersonRequestEvent(personRequest);

        listener.handlePersonRequestEvent(event);

        verify(jmsTemplate).convertAndSend("00010000000000000000123700101002870000DOE,JANE                      ");
        verify(repository).markAsSent(eq(123L), captor.capture());
        verifyNoMoreInteractions(jmsTemplate, repository);

        assertThat(captor.getValue()).isCloseTo(LocalDateTime.now(), within(1, ChronoUnit.SECONDS));
    }

    @Test
    void handlePersonRequestCreatedEventWithJmsFailure() {
        doThrow(new DestinationResolutionException("Server is down"))
                .when(jmsTemplate).convertAndSend(any());

        var personRequest = PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .id(123)
                .niss("70010100287")
                .sent(false)
                .retryCount(0)
                .firstname(" Jâne ")
                .lastname(" Doé ")
                .created(LocalDateTime.now())
                .build();
        var event = new PersonRequestEvent(personRequest);

        listener.handlePersonRequestEvent(event);

        verify(jmsTemplate).convertAndSend("00010000000000000000123700101002870000DOE,JANE                      ");
        verify(repository).updateStatus(eq(123L), isNull(), contains("Server is down"), captor.capture());
        verifyNoMoreInteractions(jmsTemplate, repository);

        assertThat(captor.getValue()).isCloseTo(LocalDateTime.now(), within(1, ChronoUnit.SECONDS));
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
            # Code,  Desc,                   Outcome, Status
            -0002,   Citizen already exists, SUCCESS, EXISTS
            -0001,   Invalid INSS,           FAILED,  INVALID_SSIN
            +0000,   Unknown code: 0,        FAILED,  ERROR
            +0001,   NULL,                   SUCCESS, CREATED
            """)
    void handleReplyCloudEvent(String returnCode, String description, String outcome, PersonCreatedStatus status)
            throws JMSException {
        LocalDateTime now = LocalDateTime.now();
        var personRequest = PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .id(789)
                .niss("70010100287")
                .sent(true)
                .retryCount(0)
                .firstname("John")
                .lastname("Wick")
                .created(now)
                .updated(now.plusSeconds(10))
                .build();

        var personCreated = new PersonCreated();
        personCreated.setId(789);
        personCreated.setFirstname("John");
        personCreated.setLastname("Wick");
        personCreated.setNames("WICK,JOHN");
        personCreated.setSsin("70010100287");
        personCreated.setSuccess("SUCCESS".equals(outcome));
        personCreated.setStatus(status);

        var message = new PersonCreatedPayload();
        message.setSpecversion("");
        message.setData(personCreated);

        when(repository.updateStatus(anyLong(), anyInt(), any(), any())).thenReturn(1);
        when(repository.findById(any())).thenReturn(Optional.of(personRequest));

        if ("+0001".equals(returnCode) || "-0002".equals(returnCode)) {
            when(citizenRepository.findById(any())).thenReturn(Optional.of(new CitizenEntity()));
        }

        TextMessage textMessage = mock(TextMessage.class);
        when(textMessage.getText()).thenReturn(toMfxResponse(personRequest, returnCode));

        listener.handleReply(textMessage);

        verify(repository).updateStatus(eq(789L), eq(Integer.parseInt(returnCode)), eq(description), captor.capture());
        assertThat(captor.getValue()).isCloseTo(LocalDateTime.now(), within(1, ChronoUnit.SECONDS));

        verify(repository).findById(789L);

        verify(amqpTemplate).convertAndSend(personCreatedPayloadArgumentCaptor.capture());

        var sentMessage = personCreatedPayloadArgumentCaptor.getValue();
        assertThat(message.getData()).isEqualTo(sentMessage.getData());

        verifyNoMoreInteractions(repository, http, amqpTemplate);
        verifyNoInteractions(jmsTemplate);
    }

    @Test
    void handleReplyWithUnexpectedAffectedRows() throws JMSException {
        LocalDateTime now = LocalDateTime.now();
        var personRequest = PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .id(789)
                .niss("70010100287")
                .sent(true)
                .retryCount(0)
                .firstname("John")
                .lastname("Wick")
                .created(now)
                .updated(now.plusSeconds(10))
                .build();

        when(repository.updateStatus(anyLong(), anyInt(), any(), any())).thenReturn(0);

        TextMessage textMessage = mock(TextMessage.class);
        when(textMessage.getText()).thenReturn(toMfxResponse(personRequest, "+0001"));

        assertThatThrownBy(() -> listener.handleReply(textMessage))
                .hasMessage("Could not update return code of PersonRequest with id: 789")
                .isExactlyInstanceOf(IllegalArgumentException.class);

        verifyNoMoreInteractions(repository);
        verifyNoInteractions(jmsTemplate, http);
    }

    @Test
    void retryFailedRequests() {
        var personRequest = PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .id(666)
                .niss("70010100287")
                .sent(false)
                .retryCount(0)
                .firstname("Tôtò")
                .lastname("Le Héro")
                .created(LocalDateTime.now())
                .build();

        when(repository.findFirst10BySentFalseOrderByCreatedAsc())
                .thenReturn(List.of(personRequest));

        listener.retryFailedRequests();

        assertThat(personRequest.getRetryCount()).isEqualTo(1);
        assertThat(personRequest.isSent()).isTrue();
        assertThat(personRequest.getUpdated()).isBetween(personRequest.getCreated(), LocalDateTime.now());

        verify(jmsTemplate).convertAndSend("00010000000000000000666700101002870000LE HERO,TOTO                  ");
        verifyNoMoreInteractions(jmsTemplate, repository);
    }

    @Test
    void errorMessage() {
        var ex = new RuntimeException(new IllegalArgumentException("aaaaaaarrg".repeat(210)));
        assertThat(ex.getCause().getMessage()).hasSize(2100);
        assertThat(listener.getErrorMessage(ex)).hasSize(2000);
    }

    String toMfxResponse(PersonRequest req, String error) {
        String record = req.toRecord();
        return record.substring(0, 23) + record.substring(38) + record.substring(23, 34) + error;
    }
}