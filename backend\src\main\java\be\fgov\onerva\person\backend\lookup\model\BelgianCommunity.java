package be.fgov.onerva.person.backend.lookup.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@SuperBuilder
@Getter
@Jacksonized @ToString(callSuper = true)
public class BelgianCommunity extends LookupData {

    private CodeMultiple codeMultiple;

    @Builder
    @Getter
    @Jacksonized @ToString
    public static class CodeMultiple {
        @JsonProperty("code.nisCode")
        private int nisCode;
        @JsonProperty("code.code")
        private int code;
    }
}
