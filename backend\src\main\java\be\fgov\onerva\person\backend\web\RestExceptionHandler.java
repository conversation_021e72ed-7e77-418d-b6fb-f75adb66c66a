package be.fgov.onerva.person.backend.web;

import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;

@Slf4j
@RestControllerAdvice
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler({
            IllegalArgumentException.class,
            UnsupportedOperationException.class
    })
    ResponseEntity<Object> handleRuntimeException(RuntimeException e, WebRequest req) {
        var pd = createProblemDetail(e, HttpStatus.BAD_REQUEST, e.getMessage(), null, null, req);
        return handleExceptionInternal(e, pd, new HttpHeaders(), HttpStatus.BAD_REQUEST, req);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        Object[] details = ex.getDetailMessageArguments();
        ex.getBody().setProperty("global", details[0]);
        ex.getBody().setProperty("fields", details[1]);
        return handleExceptionInternal(ex, null, headers, status, request);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        ProblemDetail body = createProblemDetail(ex, status, ex.getMessage(), null, null, request);

        return handleExceptionInternal(ex, body, headers, status, request);
    }

    @ExceptionHandler
    ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException cve, WebRequest req) {
        List<String> violations = cve.getConstraintViolations().stream().map(
                cv -> cv.getPropertyPath() + ": " + cv.getMessage()
        ).toList();
        var pd = createProblemDetail(cve, HttpStatus.BAD_REQUEST, cve.getMessage(), null, null, req);
        pd.setTitle("Constraint Violation");
        pd.setProperty("violations", violations);

        return handleExceptionInternal(cve, pd, new HttpHeaders(), HttpStatus.BAD_REQUEST, req);
    }

    @ExceptionHandler
    ResponseEntity<Object> catchAllException(Exception ex, WebRequest req) {
        var pd = createProblemDetail(ex, HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage(), null, null, req);

        return handleExceptionInternal(ex, pd, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, req);
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode statusCode, WebRequest request) {
        Throwable rootCause = ExceptionUtils.getRootCause(ex);
        if (statusCode.is4xxClientError()) {
            log.info(rootCause.getMessage(), rootCause);
        } else if (statusCode.is5xxServerError()) {
            log.error(rootCause.getMessage(), rootCause);
        }

        return super.handleExceptionInternal(ex, body, headers, statusCode, request);
    }
}
