package be.fgov.onerva.person.backend.request.persistence;

import be.fgov.onerva.person.backend.request.model.PersonRequest;
import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PersonRequestRepository extends JpaRepository<PersonRequest, Long>, JpaSpecificationExecutor<PersonRequest> {

    @Modifying
    @Query("update PersonRequest req set req.sent = true, req.updated = :timestamp where req.id = :reqId")
    void markAsSent(@Param("reqId") long reqId, @Param("timestamp") LocalDateTime timestamp);

    @Modifying
    @Query("update PersonRequest req set req.returnCode = :code, req.error = :error, req.updated = :timestamp where req.id = :reqId")
    int updateStatus(@Param("reqId") long reqId, @Param("code") Integer code, @Param("error") String error, @Param("timestamp") LocalDateTime timestamp);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints(@QueryHint(name = "jakarta.persistence.lock.timeout", value = "5000"))
    List<PersonRequest> findFirst10BySentFalseOrderByCreatedAsc();
}
