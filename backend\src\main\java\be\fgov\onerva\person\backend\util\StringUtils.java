package be.fgov.onerva.person.backend.util;

import static org.apache.commons.lang3.StringUtils.*;

import java.util.Locale;

/**
 * Utility class for string manipulation specific to mainframe formatting needs.
 */
public class StringUtils {
    /**
     * Private constructor to prevent instantiation.
     */
    private StringUtils() {
        super();
    }

    /**
     * Truncate the input string to the specified length and pad it with spaces on
     * the right.
     * This creates a left-aligned field of exact length.
     *
     * @param input  the input string, coerced to an empty string if null
     * @param length the length to truncate/pad to
     * @return the truncated and right-padded string of exact length
     */
    public static String truncateAndPadRight(String input, int length) {
        return rightPad(truncate(input != null ? input : "", length), length);
    }

    /**
     * Truncate the input string to the specified length and pad it with spaces on
     * the left.
     * This creates a right-aligned field of exact length.
     *
     * @param input  the input string, coerced to an empty string if null
     * @param length the length to truncate/pad to
     * @return the truncated and left-padded string of exact length
     */
    public static String truncateAndPadLeft(String input, int length) {
        return leftPad(truncate(input != null ? input : "", length), length);
    }

    /**
     * Truncate the input string to the specified length and pad it with the
     * specified character on the left.
     * This creates a right-aligned field of exact length with custom padding
     * character.
     *
     * @param input     the input string, coerced to an empty string if null
     * @param length    the length to truncate/pad to
     * @param character the character to use for padding
     * @return the truncated and left-padded string of exact length
     */
    public static String truncateAndPadLeft(String input, int length, char character) {
        if (input == null) {
            return leftPad("", length, character);
        }
        return leftPad(truncate(input, length), length, character);
    }

    /**
     * Converts text to uppercase with accent stripping and length management.
     * This is useful for mainframe formatting where accented characters are not
     * supported.
     *
     * @param text      the text to convert, coerced to an empty string if null
     * @param maxLength the maximum length to truncate to
     * @return the uppercase, accent-stripped, and truncated string
     */
    public static String toUpperClean(String text, int maxLength) {
        if (text == null) {
            return "";
        }

        String trimmed = text.trim();
        String formatted = trimmed.length() > maxLength ? trimmed.substring(0, maxLength) : trimmed;

        // First convert to uppercase
        String upperCase = formatted.toUpperCase(Locale.ROOT);

        // Handle special cases for French/Dutch characters
        upperCase = upperCase
                .replace("BOÎTE", "BOITE")
                .replace("BOITE", "BTE");

        // Use StringUtils.stripAccents for general accent stripping
        String result = org.apache.commons.lang3.StringUtils.stripAccents(upperCase);

        // Replace any remaining non-ASCII characters with their ASCII equivalents
        return result.replaceAll("[^\\p{ASCII}]", "");
    }
}
