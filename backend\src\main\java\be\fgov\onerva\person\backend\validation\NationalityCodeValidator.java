package be.fgov.onerva.person.backend.validation;

import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.lookup.model.LookupData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Nationality code validator that uses the LookupClient to verify nationality codes
 * against the CBSS nationality codes lookup service.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NationalityCodeValidator {

    private final LookupClient lookupClient;

    /**
     * Validates a nationality code against the CBSS nationality codes lookup service.
     *
     * @param nationalityCode the nationality code to validate (can be null)
     * @return true if the nationality code is valid, false otherwise
     */
    public boolean isValid(Integer nationalityCode) {
        if (nationalityCode == null) {
            return false;
        }

        try {
            Set<String> validCodes = getValidNationalityCodes();
            return validCodes.contains(nationalityCode.toString());
        } catch (Exception e) {
            log.error("Error validating nationality code {}: {}", nationalityCode, e.getMessage());
            return false;
        }
    }

    /**
     * Validates a nationality code and provides detailed error information.
     *
     * @param nationalityCode the nationality code to validate
     * @return ValidationResult containing validation status and error details
     */
    public ValidationResult validateWithDetails(Integer nationalityCode) {
        if (nationalityCode == null) {
            return new ValidationResult(false, "Nationality code cannot be null");
        }

        if (nationalityCode < 1 || nationalityCode > 999) {
            return new ValidationResult(false, "Nationality code must be between 1 and 999");
        }

        try {
            Set<String> validCodes = getValidNationalityCodes();
            if (validCodes.contains(nationalityCode.toString())) {
                return new ValidationResult(true, "Nationality code is valid");
            } else {
                return new ValidationResult(false, 
                    String.format("Nationality code %d is not found in CBSS nationality codes", nationalityCode));
            }
        } catch (Exception e) {
            log.error("Error validating nationality code {}: {}", nationalityCode, e.getMessage());
            return new ValidationResult(false, 
                String.format("Error validating nationality code: %s", e.getMessage()));
        }
    }

    /**
     * Gets all valid nationality codes from the lookup service.
     * Results are cached to improve performance.
     *
     * @return Set of valid nationality code strings
     */
    @Cacheable("nationalityCodes")
    public Set<String> getValidNationalityCodes() {
        log.debug("Fetching nationality codes from lookup service");
        List<LookupData> nationalityCodes = lookupClient.findAllNationalityCodes();
        
        Set<String> codes = nationalityCodes.stream()
                .map(LookupData::getCode)
                .collect(Collectors.toSet());
        
        log.debug("Retrieved {} nationality codes from lookup service", codes.size());
        return codes;
    }

//    /**
//     * Gets the description for a nationality code in the specified language.
//     *
//     * @param nationalityCode the nationality code
//     * @param language the language ("fr" for French, "nl" for Dutch)
//     * @return the nationality description or null if not found
//     */
//    public String getNationalityDescription(Integer nationalityCode, String language) {
//        if (nationalityCode == null) {
//            return null;
//        }
//
//        try {
//            List<LookupData> nationalityCodes = lookupClient.findAllNationalityCodes();
//            return nationalityCodes.stream()
//                    .filter(lookup -> lookup.getCode().equals(nationalityCode.toString()))
//                    .findFirst()
//                    .map(lookup -> "fr".equalsIgnoreCase(language) ? lookup.getDescFr() : lookup.getDescNl())
//                    .orElse(null);
//        } catch (Exception e) {
//            log.error("Error getting nationality description for code {}: {}", nationalityCode, e.getMessage());
//            return null;
//        }
//    }

    /**
     * Result of nationality code validation with detailed information.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, message='%s'}", valid, message);
        }
    }
}
