package be.fgov.onerva.person.backend;

import be.fgov.onerva.wave.api.UserApi;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.test.web.servlet.assertj.MockMvcTester;

/**
 * Annotation used by the integration tests, so we have a common context between all the tests
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({"unit", "unsecured"})
@AutoConfigureMockMvc
//@Transactional
public abstract class IntegrationTest {
    @Autowired
    protected MockMvcTester rest;
    @Autowired
    protected ObjectMapper json;
    @MockitoBean
    protected JmsTemplate jmsTemplate;
    @MockitoBean
    protected UserApi userApi;
    @MockitoBean
    private OAuth2AuthorizedClientManager oAuth2; // just mock the dependency ... see UserApi

    protected String toJson(Object o) {
        try {
            return json.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
