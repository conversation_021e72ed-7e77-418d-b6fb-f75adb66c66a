package be.fgov.onerva.person.backend.request;

import backend.api.CitizenRequestsApi;
import backend.rest.model.CitizenRequestDTO;
import backend.rest.model.CitizenRequestPageDTO;
import be.fgov.onerva.person.backend.request.mapper.PersonRequestMapper;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import io.github.perplexhub.rsql.RSQLJPASupport;
import lombok.RequiredArgsConstructor;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class PersonRequestController implements CitizenRequestsApi {

    private final PersonRequestRepository repository;
    private PersonRequestMapper mapper = Mappers.getMapper(PersonRequestMapper.class);

    @Override
    public ResponseEntity<CitizenRequestDTO> getById(Long id) {
        return ResponseEntity.of(repository.findById(id).map(mapper::toDto));
    }

    @Override
    public ResponseEntity<CitizenRequestPageDTO> searchCitizenRequests(String query, Integer pageNumber, Integer pageSize, String sort, Pageable pageable) {
        Page<PersonRequest> page = repository.findAll(RSQLJPASupport.rsql(query), pageable);
        return ResponseEntity.ok(mapper.toDto(page));
    }
}
