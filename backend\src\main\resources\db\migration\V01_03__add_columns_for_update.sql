
ALTER TABLE PERSON_REQUEST
    ALTER COLUMN firstname <PERSON><PERSON><PERSON><PERSON>(60) NULL;

ALTER TABLE PERSON_REQUEST
    ALTER COLUMN lastname VARCHAR(60) NULL;

ALTER TABLE PERSON_REQUEST ADD
    type                CHAR(6) NOT NULL DEFAULT 'CREATE',
    nationality_code    SMALLINT,
    language            CHAR(2),
    payment_type        VARCHAR(20),
    value_date          DATE,
    union_due           BIT,
    street              VARCHAR(255),
    number              VARCHAR(10),
    box                 VARCHAR(10),
    zip                 SMALLINT,
    city                VARCHAR(60),
    username            VARCHAR(20),
    operator_code       SMALLINT;