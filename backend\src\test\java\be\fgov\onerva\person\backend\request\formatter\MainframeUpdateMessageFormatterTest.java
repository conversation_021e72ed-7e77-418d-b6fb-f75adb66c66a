package be.fgov.onerva.person.backend.request.formatter;

import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
@DisplayName("MainframeUpdateMessageFormatter Tests")
class MainframeUpdateMessageFormatterTest {

        private MainframeUpdateMessageFormatter formatter;

        @BeforeEach
        void setUp() {
                formatter = new MainframeUpdateMessageFormatter();
        }

        @Test
        @DisplayName("Should format complete update message with all fields")
        void formatUpdateMessage_withAllFields_shouldReturnCorrectFormat() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(123456789L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .operatorCode(1234)
                                .valueDate(LocalDate.of(2024, 11, 15))
                                .address(Address.builder()
                                                .street("Chaussée de Charleroi")
                                                .number("123")
                                                .box("A")
                                                .zip("1000")
                                                .city("Brussels")
                                                .countryCode(1)
                                                .build())
                                .birthDate(LocalDate.of(1990, 5, 15))
                                .languageCode(1) // French
                                .iban("****************")
                                .bic("GKCCBEBB")
                                .accountHolder("John Doe")
                                .paymentType(PaymentType.BANK_TRANSFER)
                                .bankInfoValueDate(LocalDate.of(2024, 11, 20))
                                .unionDue(true)
                                .unionDueValueDate(LocalDate.of(2024, 11, 25))
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);

                // Verify base structure (38 chars)
                assertThat(result.substring(0, 4)).isEqualTo("0002"); // Function code
                assertThat(result.substring(4, 23)).isEqualTo("0000000000123456789"); // Message ref
                assertThat(result.substring(23, 34)).isEqualTo("***********"); // NISS
                assertThat(result.substring(34, 38)).isEqualTo("1234"); // Operator code

                // Verify signaletic part starts at position 38
                assertThat(result.substring(38, 46)).isEqualTo("********"); // Value date
                assertThat(result.substring(46, 76)).contains("CHAUSSEE DE CHARLEROI 123 A"); // Address (uppercase,
                                                                                              // accents stripped)
                assertThat(result.substring(76, 86)).contains("1000"); // Zip
                assertThat(result.substring(86, 116)).contains("BRUSSELS"); // City (uppercase)
                assertThat(result.substring(116, 119)).isEqualTo("001"); // Country code
                assertThat(result.substring(119, 127)).isEqualTo("********"); // Birth date
                assertThat(result.substring(127, 128)).isEqualTo("1"); // Language code

                // Verify bank part starts at position 140 (38 + 102)
                int bankStart = 140;
                assertThat(result.substring(bankStart, bankStart + 34)).contains("****************"); // IBAN
                assertThat(result.substring(bankStart + 34, bankStart + 45)).contains("GKCCBEBB"); // BIC
                assertThat(result.substring(bankStart + 45, bankStart + 75)).contains("JOHN DOE"); // Account holder
                                                                                                   // (uppercase)
                assertThat(result.substring(bankStart + 75, bankStart + 76)).isEqualTo("1"); // Payment type
                assertThat(result.substring(bankStart + 76, bankStart + 84)).isEqualTo("********"); // Bank value date

                // Verify union due part starts at position 230 (38 + 102 + 90)
                int unionStart = 230;
                assertThat(result.substring(unionStart, unionStart + 1)).isEqualTo("1"); // Union due flag
                assertThat(result.substring(unionStart + 1, unionStart + 9)).isEqualTo("********"); // Union due value
                                                                                                    // date
        }

        @Test
        @DisplayName("Should format minimal update message with only required fields")
        void formatUpdateMessage_withMinimalFields_shouldReturnCorrectFormat() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(1L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 12, 1))
                                .address(Address.builder()
                                                .street("Main Street")
                                                .zip("2000")
                                                .city("Antwerp")
                                                .build())
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);

                // Verify base structure
                assertThat(result.substring(0, 4)).isEqualTo("0002");
                assertThat(result.substring(4, 23)).isEqualTo("0000000000000000001");
                assertThat(result.substring(23, 34)).isEqualTo("***********");
                assertThat(result.substring(34, 38)).isEqualTo("0000"); // Default operator code

                // Verify signaletic part
                assertThat(result.substring(38, 46)).isEqualTo("********"); // Value date
                assertThat(result.substring(46, 76)).contains("MAIN STREET"); // Address (uppercase)
                assertThat(result.substring(76, 86)).contains("2000"); // Zip
                assertThat(result.substring(86, 116)).contains("ANTWERP"); // City (uppercase)
                assertThat(result.substring(116, 119)).isEqualTo("001"); // Default country code (Belgium)
                assertThat(result.substring(119, 127)).isEqualTo("        "); // No birth date (8 spaces)
                assertThat(result.substring(127, 128)).isEqualTo(" "); // No language code

                // Verify bank part is empty/spaces
                int bankStart = 140;
                assertThat(result.substring(bankStart, bankStart + 34)).matches("\\s{34}"); // IBAN spaces
                assertThat(result.substring(bankStart + 75, bankStart + 76)).isEqualTo(" "); // No payment type

                // Verify union due part
                int unionStart = 230;
                assertThat(result.substring(unionStart, unionStart + 1)).isEqualTo(" "); // No union due flag
                assertThat(result.substring(unionStart + 1, unionStart + 9)).isEqualTo("********"); // Default to main
                                                                                                    // value date
        }

        @Test
        @DisplayName("Should handle foreign address with foreign zip code")
        void formatUpdateMessage_withForeignAddress_shouldUseForeignZipCode() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(999L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 10, 10))
                                .address(Address.builder()
                                                .street("Rue de la Paix")
                                                .number("42")
                                                .city("Brussels")
                                                .foreignZipCode("75001") // Foreign zip code should take precedence
                                                .city("Paris") // Foreign city should take precedence
                                                .countryCode(4) // France
                                                .build())
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);

                // Verify foreign zip code is used instead of Belgian zip
                assertThat(result.substring(76, 86)).contains("75001");

                // Verify foreign city is used (uppercase)
                assertThat(result.substring(86, 116)).contains("PARIS");

                // Verify country code
                assertThat(result.substring(116, 119)).isEqualTo("250");
        }

        @Test
        @DisplayName("Should handle bank information with default value dates")
        void formatUpdateMessage_withBankInfoDefaultValueDate_shouldUseMainValueDate() {
                // Given
                LocalDate mainValueDate = LocalDate.of(2024, 8, 15);
                PersonRequest request = PersonRequest.builder()
                                .id(555L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(mainValueDate)
                                .address(Address.builder()
                                                .street("Bank Street")
                                                .zip("3000")
                                                .city("Leuven")
                                                .build())
                                .iban("****************")
                                .bic("TESTBEBB")
                                .accountHolder("Test User")
                                .paymentType(PaymentType.OTHER_BANK_TRANSFER)
                                // No bankInfoValueDate - should default to main valueDate
                                .unionDue(false)
                                // No unionDueValueDate - should default to main valueDate
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);

                // Verify bank value date defaults to main value date
                int bankStart = 140;
                assertThat(result.substring(bankStart + 76, bankStart + 84)).isEqualTo("********");

                // Verify payment type
                assertThat(result.substring(bankStart + 75, bankStart + 76)).isEqualTo("2");

                // Verify union due value date defaults to main value date
                int unionStart = 230;
                assertThat(result.substring(unionStart, unionStart + 1)).isEqualTo("0"); // Union due false
                assertThat(result.substring(unionStart + 1, unionStart + 9)).isEqualTo("********");
        }

        @Test
        @DisplayName("Should handle field truncation for long values")
        void formatUpdateMessage_withLongValues_shouldTruncateFields() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(777L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 7, 7))
                                .address(Address.builder()
                                                .street("This is a very long street name that exceeds the maximum allowed length")
                                                .number("999999999999") // Very long number
                                                .box("VERYLONGBOXNAME")
                                                .zip("9999")
                                                .city("This is a very long city name that exceeds maximum length")
                                                .build())
                                .iban("****************567890***********2345678") // Too long IBAN
                                .bic("VERYLONGBIC") // Too long BIC
                                .accountHolder("This is a very long account holder name that exceeds the maximum allowed length")
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);

                // Verify address is truncated to 30 chars
                String addressPart = result.substring(46, 76);
                assertThat(addressPart).hasSize(30);

                // Verify city is truncated to 30 chars
                String cityPart = result.substring(86, 116);
                assertThat(cityPart).hasSize(30);

                // Verify IBAN is truncated to 34 chars
                int bankStart = 140;
                String ibanPart = result.substring(bankStart, bankStart + 34);
                assertThat(ibanPart).hasSize(34);

                // Verify BIC is truncated to 11 chars
                String bicPart = result.substring(bankStart + 34, bankStart + 45);
                assertThat(bicPart).hasSize(11);

                // Verify account holder is truncated to 30 chars
                String accountHolderPart = result.substring(bankStart + 45, bankStart + 75);
                assertThat(accountHolderPart).hasSize(30);
        }

        @Test
        @DisplayName("Should throw exception for null PersonRequest")
        void formatUpdateMessage_withNullRequest_shouldThrowException() {
                // When & Then
                assertThatThrownBy(() -> formatter.formatUpdateMessage(null))
                                .isInstanceOf(IllegalArgumentException.class)
                                .hasMessage("PersonRequest cannot be null");
        }

        @Test
        @DisplayName("Should format message with exact 360 characters")
        void formatUpdateMessage_shouldAlwaysReturn360Characters() {
                // Given - various requests with different field combinations
                PersonRequest[] requests = {
                                PersonRequest.builder().id(1L).type(PersonRequestType.UPDATE).niss("***********")
                                                .valueDate(LocalDate.now()).build(),
                                PersonRequest.builder().id(2L).type(PersonRequestType.UPDATE).niss("***********")
                                                .valueDate(LocalDate.now())
                                                .address(Address.builder().street("Test").zip("1000").city("Test")
                                                                .build())
                                                .build(),
                                PersonRequest.builder().id(3L).type(PersonRequestType.UPDATE).niss("***********")
                                                .valueDate(LocalDate.now())
                                                .iban("****************").unionDue(true).build()
                };

                for (PersonRequest request : requests) {
                        // When
                        String result = formatter.formatUpdateMessage(request);

                        // Then
                        assertThat(result).hasSize(MainframeUpdateMessageFormatter.TOTAL_MESSAGE_LENGTH);
                }
        }

        @Test
        @DisplayName("Should handle all payment types correctly")
        void formatUpdateMessage_withDifferentPaymentTypes_shouldFormatCorrectly() {
                // Test each payment type
                PaymentType[] paymentTypes = { PaymentType.BANK_TRANSFER, PaymentType.OTHER_BANK_TRANSFER,
                                PaymentType.CIRCULAR_CHEQUE, null };
                String[] expectedChars = { "1", "2", "3", " " };

                for (int i = 0; i < paymentTypes.length; i++) {
                        // Given
                        PersonRequest request = PersonRequest.builder()
                                        .id((long) i)
                                        .type(PersonRequestType.UPDATE)
                                        .niss("***********")
                                        .valueDate(LocalDate.now())
                                        .paymentType(paymentTypes[i])
                                        .build();

                        // When
                        String result = formatter.formatUpdateMessage(request);

                        // Then
                        int bankStart = 140;
                        assertThat(result.substring(bankStart + 75, bankStart + 76)).isEqualTo(expectedChars[i]);
                }
        }

        @Test
        @DisplayName("Should handle language codes correctly")
        void formatUpdateMessage_withDifferentLanguageCodes_shouldFormatCorrectly() {
                // Test each language code
                Integer[] languageCodes = { 1, 2, 3, null };
                String[] expectedChars = { "1", "2", "3", " " };

                for (int i = 0; i < languageCodes.length; i++) {
                        // Given
                        PersonRequest request = PersonRequest.builder()
                                        .id((long) i)
                                        .type(PersonRequestType.UPDATE)
                                        .niss("***********")
                                        .valueDate(LocalDate.now())
                                        .languageCode(languageCodes[i])
                                        .build();

                        // When
                        String result = formatter.formatUpdateMessage(request);

                        // Then
                        assertThat(result.substring(127, 128)).isEqualTo(expectedChars[i]);
                }
        }
}
