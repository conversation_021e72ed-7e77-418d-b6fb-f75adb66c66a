package be.fgov.onerva.person.backend.validation;

import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.regex.Pattern;

/**
 * IBAN validation utility class implementing MOD-97 checksum validation
 * as specified in ISO 13616 standard and mentioned in the API specification.
 */
@Component
public class IbanValidator {

    private static final Pattern IBAN_PATTERN = Pattern.compile("^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$");
    private static final int MIN_IBAN_LENGTH = 15;
    private static final int MAX_IBAN_LENGTH = 34;

    /**
     * Validates an IBAN using both format and MOD-97 checksum validation.
     *
     * @param iban the IBAN to validate (can be null or empty)
     * @return true if the IBAN is valid, false otherwise
     */
    public boolean isValid(String iban) {
        if (iban == null || iban.trim().isEmpty()) {
            return false;
        }

        // Remove spaces and convert to uppercase
        String cleanIban = iban.replaceAll("\\s", "").toUpperCase();

        // Check basic format
        if (!isValidFormat(cleanIban)) {
            return false;
        }

        // Check MOD-97 checksum
        return isValidChecksum(cleanIban);
    }

    /**
     * Validates the basic format of an IBAN.
     *
     * @param iban the cleaned IBAN string
     * @return true if format is valid, false otherwise
     */
    private boolean isValidFormat(String iban) {
        // Check length
        if (iban.length() < MIN_IBAN_LENGTH || iban.length() > MAX_IBAN_LENGTH) {
            return false;
        }

        // Check pattern: 2 letters + 2 digits + up to 30 alphanumeric
        return IBAN_PATTERN.matcher(iban).matches();
    }

    /**
     * Validates the MOD-97 checksum of an IBAN according to ISO 13616.
     * 
     * Algorithm:
     * 1. Move the first 4 characters to the end
     * 2. Replace letters with numbers (A=10, B=11, ..., Z=35)
     * 3. Calculate mod 97 of the resulting number
     * 4. The result should be 1 for a valid IBAN
     *
     * @param iban the cleaned IBAN string
     * @return true if checksum is valid, false otherwise
     */
    private boolean isValidChecksum(String iban) {
        try {
            // Step 1: Move first 4 characters to the end
            String rearranged = iban.substring(4) + iban.substring(0, 4);

            // Step 2: Replace letters with numbers
            StringBuilder numericString = new StringBuilder();
            for (char c : rearranged.toCharArray()) {
                if (Character.isLetter(c)) {
                    // A=10, B=11, ..., Z=35
                    numericString.append(c - 'A' + 10);
                } else {
                    numericString.append(c);
                }
            }

            // Step 3: Calculate mod 97
            BigInteger number = new BigInteger(numericString.toString());
            BigInteger remainder = number.remainder(BigInteger.valueOf(97));

            // Step 4: Check if remainder is 1
            return remainder.equals(BigInteger.ONE);

        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validates an IBAN and provides detailed error information.
     *
     * @param iban the IBAN to validate
     * @return ValidationResult containing validation status and error details
     */
    public ValidationResult validateWithDetails(String iban) {
        if (iban == null || iban.trim().isEmpty()) {
            return new ValidationResult(false, "IBAN cannot be null or empty");
        }

        String cleanIban = iban.replaceAll("\\s", "").toUpperCase();

        if (!isValidFormat(cleanIban)) {
            if (cleanIban.length() < MIN_IBAN_LENGTH || cleanIban.length() > MAX_IBAN_LENGTH) {
                return new ValidationResult(false, 
                    String.format("IBAN length must be between %d and %d characters", MIN_IBAN_LENGTH, MAX_IBAN_LENGTH));
            }
            if (!IBAN_PATTERN.matcher(cleanIban).matches()) {
                return new ValidationResult(false, "IBAN format is invalid. Expected: 2 letters + 2 digits + up to 30 alphanumeric characters");
            }
        }

        if (!isValidChecksum(cleanIban)) {
            return new ValidationResult(false, "IBAN checksum validation failed (MOD-97)");
        }

        return new ValidationResult(true, "IBAN is valid");
    }

    /**
     * Result of IBAN validation with detailed information.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, message='%s'}", valid, message);
        }
    }
}
