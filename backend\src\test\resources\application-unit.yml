spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jms.artemis.ArtemisAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
  jpa:
    hibernate:
      ddl-auto: none
  sql:
    init:
      mode: always
      schema-locations:
        - classpath:scripts/init.sql
  jackson:
    time-zone: Europe/Brussels

application:
  national-citizen-default: 150

datasource:
  mfx:
    url: jdbc:tc:sqlserver:2019-latest:///mfx
  person:
    url: jdbc:tc:sqlserver:2019-latest:///person

flagsmith:
  environment:
    id: "QFA37efDPrx4J6YsP5mHJc"
  api: "https://flagsmith.prod.paas.onemrva.priv/api/v1/"

#logging:
#  level:
#    reactor:
#      netty: TRACE
#    org:
#      springframework: DEBUG
#    au:
#      com:
#        dius:
#          pact: DEBUG

ibm:
  mq:
    autoConfigure: false

onerva:
  observability:
    prometheus:
      enabled: false