package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoContratDs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CitizenInfoContratDsRepository extends JpaRepository<CitizenInfoContratDs, Integer>, JpaSpecificationExecutor<CitizenInfoContratDs> {

    List<CitizenInfoContratDs> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox, int flagValid, int dateValid);
}
