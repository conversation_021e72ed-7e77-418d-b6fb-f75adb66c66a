package be.fgov.onerva.person.backend.citizen.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "keybox_ds", schema = "dbo")
public class CitizenEntity {

    @Id
    @Column(name = "num_pens")
    private int id;

    @Column(name = "nom_prenom")
    private String fullName;

    private int numBox;

    @Column(name = "code_post")
    private int zipCode;
    // add num_br for index usage on nom_prenom
    private int numBr;
    // ssin versioning
    private int lastId;

    private boolean flagPersonnel;

}
