package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoSectopDs;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CitizenInfoSectopDsRepository  extends JpaRepository<CitizenInfoSectopDs, Integer>  {

      List<CitizenInfoSectopDs> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox, int flagValid, int dateValid);

}
