package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface CitizenInfoRepository extends JpaRepository<CitizenInfoEntity, Integer>, JpaSpecificationExecutor<CitizenInfoEntity> {
    Page<CitizenInfoEntity> getCitizenInfoEntitiesByNumPensInAndLastIdEquals(List<Integer> numpens, Integer lastId, Pageable pageable);

    List<CitizenInfoEntity> findByNumBoxIn(List<Integer> numBox);

    @Query("from CitizenInfoEntity c where c.numBox in (select c2.numBox from CitizenInfoEntity c2 where c2.numPens in ?1)")
    List<CitizenInfoEntity> findByNumPensIn(List<Integer> numpens);
}
