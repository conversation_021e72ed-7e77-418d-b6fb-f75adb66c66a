package be.fgov.onerva.person.backend.config;

import be.fgov.onerva.person.backend.config.props.RabbitmqOauthProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.impl.CredentialsProvider;
import com.rabbitmq.client.impl.CredentialsRefreshService;
import com.rabbitmq.client.impl.DefaultCredentialsRefreshService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration(proxyBeanMethods = false)
public class RabbitConfig {


    @ConditionalOnProperty(prefix = RabbitmqOauthProperties.PREFIX, name = "enabled")
    @EnableConfigurationProperties(RabbitmqOauthProperties.class)
    @Configuration
    static class OauthRabbitConfig {

        @Bean
        public CredentialsProvider oauthCredentialsProvider(RabbitmqOauthProperties properties) {
            return properties.toCredentialsProvider();
        }

        @Bean
        public CredentialsRefreshService credentialsRefreshService() {
            return new DefaultCredentialsRefreshService.DefaultCredentialsRefreshServiceBuilder().
                    build();
        }
    }

    @Bean
    public Jackson2JsonMessageConverter messageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public FanoutExchange personExchange() {
        return new FanoutExchange("person.exchange", true, false);
    }

}
