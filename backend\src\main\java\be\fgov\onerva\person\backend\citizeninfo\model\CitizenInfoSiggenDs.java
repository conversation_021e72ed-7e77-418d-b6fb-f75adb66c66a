package be.fgov.onerva.person.backend.citizeninfo.model;

import jakarta.persistence.Column;
import lombok.*;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "siggen_ds", schema = "dbo")
public class CitizenInfoSiggenDs {

    @Id
    private long id;

    private long parentId;

    private int flagValid;

    private int dateValid;

    @Column(name = "langue")
    private Integer language;

    @Column(name = "mode_pay")
    private Integer paymentMode;
}
