package be.fgov.onerva.person.backend.citizen.model;

import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import be.fgov.onerva.person.backend.request.model.UnionDueUpdateInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

@Builder
@Getter
@ToString
public class CitizenUpdateRequest {
    @NotNull
    @Pattern(regexp = "\\d{11}")
    private String niss;
    @NotNull
    @Valid
    private Address address;
    @Min(100)
    @Max(999)
    private Integer nationalityCode;

    /**
     * Birth date in YYYYMMDD format as per API specification
     */
    @Pattern(regexp = "\\d{8}", message = "Birth date must be in YYYYMMDD format")
    private String birthDate;

    /**
     * Language code: 1=fr, 2=nl, 3=de as per API specification
     */
    @Min(1)
    @Max(3)
    private Integer languageCode;

    /**
     * Bank information for updates - nested object
     */
    @Valid
    private BankUpdateInfo bankInfo;

    /**
     * Union due information for updates - nested object
     */
    @Valid
    private UnionDueUpdateInfo unionDueInfo;

    /**
     * @deprecated Use bankInfo.paymentType instead
     */
    @Deprecated
    private PaymentType paymentType;

    /**
     * @deprecated Use unionDueInfo.unionDue instead
     */
    @Deprecated
    private Boolean unionDue;

    @NotNull
    private LocalDate valueDate;
    @NotBlank
    private String username;
    private String correlationId;
}
