Feature: Create Citizen

  Sc<PERSON>rio: Mainframe notify person that the person is created
    Given url baseUrl
    And path "/e2e/person/created"
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method POST
    Then status 204
    Given url queueUrl
    And header Authorization = "Basic Z3Vlc3Q6Z3Vlc3Q=";
    Given path "/api/exchanges/onemrva/person.exchange"
    When method GET
    Then status 200
    * def response = response
    * def publishInValue = response.message_stats.publish_in
    * assert publishInValue >= 1
