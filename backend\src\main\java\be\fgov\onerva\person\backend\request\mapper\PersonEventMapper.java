package be.fgov.onerva.person.backend.request.mapper;

import be.fgov.onerva.person.backend.request.model.PersonMfxResponse;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.msg.v1.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import static be.fgov.onerva.person.msg.v1.PersonCreatedStatus.*;

@Mapper
public interface PersonEventMapper {

    @CloudEventMapping
    @Mapping(target = "type", constant = "be.fgov.onerva.person.msg.v1.PersonCreated")
    @Mapping(target = "data", expression = "java(mapToCreate(request, response))")
    PersonCreatedPayload mapToCloudEventCreate(PersonRequest request, PersonMfxResponse response);

    @PersonEventMapping
    @Mapping(target = "status", expression = "java(personCreatedStatus(response.getErrorCode()))")
    PersonCreated mapToCreate(PersonRequest request, PersonMfxResponse response);

    default PersonCreatedStatus personCreatedStatus(int errorCode) {
        return switch (errorCode) {
            case 1 -> CREATED;
            case -1 -> INVALID_SSIN;
            case -2 -> EXISTS;
            default -> ERROR;
        };
    };

    @CloudEventMapping
    @Mapping(target = "type", constant = "be.fgov.onerva.person.msg.v1.PersonUpdated")
    @Mapping(target = "data", expression = "java(mapToUpdate(request, response))")
    PersonUpdatedPayload mapToCloudEventUpdate(PersonRequest request, PersonMfxResponse response);

    @PersonEventMapping
    PersonUpdated mapToUpdate(PersonRequest request, PersonMfxResponse response);
}
