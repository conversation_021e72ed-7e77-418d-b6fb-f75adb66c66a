package be.fgov.onerva.person.backend.citizen.mapper;

import backend.rest.model.CitizenCreationRequestDTO;
import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

public class CitizenMapperTest {

    private static CitizenMapper mapper = Mappers.getMapper(CitizenMapper.class);

    @Test
    void map() {
        assertNull(mapper.map((CitizenEntity) null));
        assertNull(mapper.mapPageToDto(null));
        var niss = "***********";
        var citizen = CitizenEntity.builder()
                .id(881231808)
                .fullName("LASTNAME1,NAME1")
                .build();
        assertEquals(niss, mapper.map(citizen).getNiss());

        assertNull(mapper.lastName(null));
        assertNull(mapper.firstName(null));
        assertNull(mapper.niss(null));
    }

    @Test
    void citizenCreationRequest() {
        assertThrows(IllegalArgumentException.class, () ->
            mapper.map(null, "NULL", false)
        );

        var dto = new CitizenCreationRequestDTO().firstname("John").lastname("Doe").niss("***********");
        var request = mapper.map(dto, "ADMISSIBILITY", true);
        assertThat(request.isAllowance()).isTrue();
        assertThat(request.getDomain()).isSameAs(BusinessDomain.ADMISSIBILITY);
        assertThat(request.getFirstname()).isEqualTo("John");
        assertThat(request.getLastname()).isEqualTo("Doe");
        assertThat(request.getNiss()).isEqualTo("***********");
    }

}
