package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.wave.model.User;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PersonRequestServiceTest {
    @Mock
    private PersonRequestRepository repository;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @InjectMocks
    private PersonRequestService service;
    @Captor
    ArgumentCaptor<PersonRequestEvent> captor;

    @Test
    void createMinimalPersonInfo() {
        when(repository.saveAndFlush(any())).then(invocation -> {
            PersonRequest request = invocation.getArgument(0);
            ReflectionTestUtils.setField(request, "id", 33L);
            return request;
        });

        var personRequest = service.createMinimalPersonInfo("John", "Doe", "80123100751", null);

        assertThat(personRequest.getFirstname()).isEqualTo("John");
        assertThat(personRequest.getLastname()).isEqualTo("Doe");
        assertThat(personRequest.getNiss()).isEqualTo("80123100751");
        assertThat(personRequest.getId()).isEqualTo(33);
        assertThat(personRequest.getError()).isNull();
        assertThat(personRequest.getUpdated()).isNull();
        assertThat(personRequest.getRetryCount()).isEqualTo(0);
        assertThat(personRequest.getCreated()).isCloseTo(LocalDateTime.now(), within(1, ChronoUnit.SECONDS));
        assertThat(personRequest.getCorrelationId()).isNull();

        verify(repository).saveAndFlush(personRequest);
        verify(eventPublisher).publishEvent(captor.capture());

        assertThat(captor.getValue().getPersonRequest()).isSameAs(personRequest);
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
        # Code,  Expected
        NULL,    NULL
        '0001',  1
        '002B',  NULL
        """)
    void operatorCode(String opCode, Integer expected) {
        assertThat(service.getOperatorCode(new User().addOperatorCodesItem(opCode))).isEqualTo(expected);
    }
}