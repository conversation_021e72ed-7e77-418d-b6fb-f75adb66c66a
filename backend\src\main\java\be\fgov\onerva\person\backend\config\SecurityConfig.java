package be.fgov.onerva.person.backend.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.time.Duration;
import java.util.List;

@Slf4j
@Configuration
public class SecurityConfig {

    @Bean
    SecurityFilterChain securityFilterChain(HttpSecurity http, Environment env) throws Exception {
        http.csrf(c -> c.disable())
                .cors(Customizer.withDefaults());

        if (env.acceptsProfiles(Profiles.of("unsecured"))) {
            log.info("Unsecured --> permit all");
            http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll());
        } else {
            log.info("Secured --> /api");
            http.authorizeHttpRequests(authorize ->
                authorize.requestMatchers("/actuator/**", "/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/e2e/**").permitAll()
                        .requestMatchers(HttpMethod.OPTIONS).permitAll()
                        .requestMatchers("/api/**").authenticated()
            ).oauth2ResourceServer(server -> server.jwt(Customizer.withDefaults()));
        }

        return http.build();
    }

    /**
     * This decoder can be skipped during development phase when we want to avoid checking the issuer of the JWT token.
     */
    @Bean @Profile("!unsecured")
    JwtDecoder jwtDecoder(OAuth2ResourceServerProperties props, @Value("${keycloak.checkToken:true}") boolean checkIssuer) {
        String issuerUri = props.getJwt().getIssuerUri();
        var validator = checkIssuer ?
                JwtValidators.createDefaultWithIssuer(issuerUri) // check timestamp and issuer
                : JwtValidators.createDefault(); // only check timestamp
        log.info("Issuer: {}", issuerUri);
        log.info("check issuer: {}", checkIssuer);
        NimbusJwtDecoder jwtDecoder = JwtDecoders.fromOidcIssuerLocation(issuerUri);
        jwtDecoder.setJwtValidator(validator);

        return jwtDecoder;
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource(@Value("${cors.allowedOrigins:*}") List<String> origins) {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(origins);
        configuration.setAllowedMethods(List.of("*"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setMaxAge(Duration.ofHours(1));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
}