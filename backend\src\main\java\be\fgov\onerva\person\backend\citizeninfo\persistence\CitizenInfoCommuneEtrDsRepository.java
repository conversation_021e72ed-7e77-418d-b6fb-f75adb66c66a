package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoCommuneEtrDs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface CitizenInfoCommuneEtrDsRepository extends JpaRepository<CitizenInfoCommuneEtrDs, String>, JpaSpecificationExecutor<CitizenInfoCommuneEtrDs> {

    Optional<CitizenInfoCommuneEtrDs> findFirstByNumBoxAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(int numbox, int flagValid, int dateValid);
}
