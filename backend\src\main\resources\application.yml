spring:
  profiles:
    default: local
  jpa:
    show-sql: false
    open-in-view: false
    properties:
      hibernate:
        format_sql: true
  mvc:
    problem-details:
      enabled: true
  web:
    locale: en
    locale-resolver: fixed
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri:
      client:
        registration:
          keycloak:
            client-id: person-backend
            authorization-grant-type: client_credentials
  jms:
    template:
      receive-timeout: 3s
      default-destination: ${queue.in}
  data:
    web:
      pageable:
        size-parameter: pageSize
        page-parameter: pageNumber
  rabbitmq:
    virtual-host: onemrva
    template:
      exchange: person.exchange
  application:
    name: person-backend
    national-citizen-default: 150
  threads:
    virtual:
      enabled: true

application:
  national-citizen-default: 150

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      probes:
        enabled: true
  health:
    rabbit:
      enabled: false
  info:
    git:
      mode: full
    java:
      enabled: true
    os:
      enabled: true

flagsmith:
  api: https://flagsmith.prod.paas.onemrva.priv/api/v1/

onerva:
  observability:
    prometheus:
      domain: CORSEA
      system: person
      component: backend

queue:
  in: queue:///QL_UB_UNKNOWN_CITIZEN_IN?targetClient=1
  out: QL_UB_UNKNOWN_CITIZEN_OUT
