package be.fgov.onerva.person.backend;

import be.fgov.onerva.microcks.MicrocksReporter;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import io.github.microcks.testcontainers.MicrocksContainer;
import io.github.microcks.testcontainers.MicrocksException;
import io.github.microcks.testcontainers.model.TestRequest;
import io.github.microcks.testcontainers.model.TestResult;
import io.github.microcks.testcontainers.model.TestRunnerType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.jdbc.Sql;
import org.testcontainers.Testcontainers;
import org.testcontainers.images.PullPolicy;
import org.testcontainers.utility.DockerImageName;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
@Sql(value = "/scripts/init-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
class ApiTest extends IntegrationTest {
    @LocalServerPort
    private int port;
    @Autowired
    private PersonRequestRepository repository;

    private LocalDateTime now = LocalDateTime.now().withNano(0);


    public static MicrocksContainer container = new MicrocksContainer(
            DockerImageName.parse("docker-proxy.onemrva.priv/microcks/microcks-uber:1.10.1")
                    .asCompatibleSubstituteFor("quay.io/microcks/microcks-uber"))
                    .withImagePullPolicy(PullPolicy.alwaysPull());
    //region data setup
    @AfterEach
    void tearDown() {
        repository.deleteAll();
    }


    private PersonRequest _jane = PersonRequest.builder()
            .type(PersonRequestType.CREATE)
            .niss("70010100287")
            .id(0)
            .firstname("Jane")
            .lastname("Doe")
            .created(now)
            .build();

    private PersonRequest _john = PersonRequest.builder()
            .type(PersonRequestType.CREATE)
            .niss("70010100188")
            .id(1)
            .firstname("John")
            .lastname("Doe")
            .created(now)
            .build();
    //endregion

    private List<String> listToTest = List.of(
            "GET /citizen/{niss}",
            "GET /citizen/numbox/{numbox}",
            "GET /citizen",
//           FIXME: array query param are broken in microcks see : https://github.com/microcks/microcks/issues/1016
//            "GET /citizen/info",
            "GET /citizen/requests"
    );

    @Test
    public void testApiContract() throws MicrocksException, IOException, InterruptedException {
        Testcontainers.exposeHostPorts(port);
        container.withAccessToHost(true);
        container.addEnv("LOGGING_LEVEL_ORG_SPRINGFRAMEWORK", "DEBUG");
        container.start();
        container.importAsMainArtifact(new File("../api/person_api.yaml"));

//        repository.saveAndFlush(_jane);
//        repository.saveAndFlush(_john);

        TestRequest req = new TestRequest.Builder()
                .serviceId("Person API:1.0.0")
                .runnerType(TestRunnerType.OPEN_API_SCHEMA.name())
                .testEndpoint(String.format("http://host.testcontainers.internal:%d/api", port))
                .filteredOperations(listToTest)
                .timeout(20000L)
                .build();
        TestResult testResult = container.testEndpoint(req);
        System.out.println("MICROCKS PORT: " + container.getMappedPort(8080));
        var report = MicrocksReporter.export(testResult, container.getHttpEndpoint());
        container.stop();
        report.testResult().getTestCaseResults().forEach(testRes -> {
            testRes.getTestStepResults().forEach(result -> {
                if(StringUtils.isNoneBlank(result.getMessage())){
                    log.warn("Microcks message for {} : {}",testRes.getOperationName(), result.getMessage());
                }
            });
        });
        Assertions.assertTrue(testResult.isSuccess());
        assertEquals(listToTest.size(), report.messages().size());
    }
}
