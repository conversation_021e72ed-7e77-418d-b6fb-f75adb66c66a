package be.fgov.onerva.person.backend.citizeninfo.mapper;

import lombok.experimental.UtilityClass;

import java.time.LocalDate;

@UtilityClass
public final class DateUtils {

    private static final int NULL_DATE = 9999_99_99;

    public static LocalDate convertToDate(Integer dateInteger) {
        if (dateInteger == null ||  dateInteger >= NULL_DATE) {
            return null;
        }
        try {
            return LocalDate.of(
                    dateInteger / 10_000,
                    (dateInteger % 10_000) / 100,
                    dateInteger % 100
            );
        } catch (RuntimeException e) {
            return null;
        }
    }

    public static Integer convertToInt(LocalDate date) {
        if (date == null) {
            return null;
        }
        return (date.getYear() * 10_000) + (date.getMonthValue() * 100) +  date.getDayOfMonth();
    }

    public static int currentDate() {
        return convertToInt(LocalDate.now());
    }
}
