package be.fgov.onerva.person.backend.e2e;

import be.fgov.onerva.person.backend.config.HttpClientConfig;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@Profile(HttpClientConfig.DEV_PROFILE)
@RequestMapping("/e2e")
public class QueueMainframeE2EController {

    public static final String SSIN = "99062404991";
    @Autowired
    private JmsTemplate jmsTemplate;

    @Autowired
    private PersonRequestRepository personRequestRepository;

    @Transactional
    @PostMapping("/person/created")
    public ResponseEntity<Void> postCitizenCreatedInQueueOut() throws InterruptedException {
        var personRequest = personRequestRepository.saveAndFlush(createPersonRequest());
        jmsTemplate.convertAndSend("DOE,JANE                      700101002870000000000000000" + convertPersonId(personRequest.getId()) + "+0001");
        Thread.sleep(3000);
        return ResponseEntity.noContent().build();
    }

    private String convertPersonId(long personId) {
        if (personId < 10) {
            return "00" + personId;
        }
        if (personId < 100) {
            return "0" + personId;
        }
        return "" + personId;
    }

    private PersonRequest createPersonRequest() {
        return PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .created(LocalDateTime.now())
                .niss(SSIN)
                .firstname("John")
                .lastname("Doe")
                .sent(true)
                .updated(LocalDateTime.now())
                .build();
    }

}
