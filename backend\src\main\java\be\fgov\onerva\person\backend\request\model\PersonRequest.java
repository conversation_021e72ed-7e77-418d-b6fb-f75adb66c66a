package be.fgov.onerva.person.backend.request.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Formatter;
import java.util.Locale;

@Builder
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor
@Getter
@Entity
public class PersonRequest {

    public static final int ERROR_LENGTH = 2000;

    static final int INSS_LENGTH = 11;
    static final int NAMES_LENGTH = 30;
    static final int ID_LENGTH = 19;
    static final int CODE_LENGTH = 5;
    static final int MAX_LAST_NAME_SIZE = 24;
    static final int MAX_ADDRESS_SIZE = 30;

    public static final String ID_FORMAT = "%0" + ID_LENGTH + 'd';

    static final String CREATE = "0001";
    static final String UPDATE = "0002";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @NotNull
    @Column(updatable = false)
    private LocalDateTime created;
    @NotNull
    @Pattern(regexp = "\\d{11}")
    private String niss;
    private String firstname;
    private String lastname;
    private boolean sent;
    private LocalDateTime updated;
    private int retryCount;
    private Integer returnCode;
    private String error;
    private String correlationId;
    @NotNull
    @Enumerated(EnumType.STRING)
    private PersonRequestType type;
    private Integer nationalityCode;
    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;
    private Boolean unionDue;
    private LocalDate valueDate;
    private Address address;
    private String username;
    private Integer operatorCode;

    // New fields for foreign addresses, bank info, and birthdate
    private LocalDate birthDate;
    @Column(length = 34)
    private String iban;
    @Column(length = 11)
    private String bic;
    @Column(length = 30)
    private String accountHolder;
    @Min(1)
    @Max(3)
    private Integer languageCode;
    private LocalDate bankInfoValueDate;
    private LocalDate unionDueValueDate;

    public void incrementRetryCount() {
        retryCount++;
    }

    public void markAsSent() {
        sent = true;
        updated = LocalDateTime.now();
    }

    public void logError(String error) {
        this.error = error;
        updated = LocalDateTime.now();
    }

    public String toRecord() {
        if (type == PersonRequestType.CREATE) {
            return toCreateRecord();
        }
        if (type == PersonRequestType.UPDATE) {
            return toUpdateRecord();
        }
        throw new IllegalStateException("Unknown type: " + type);
    }

    String toCreateRecord() {
        StringBuilder builder = new StringBuilder(CREATE);
        new Formatter(builder).format(ID_FORMAT, getId());
        builder.append(getNiss())
                .append(getOperatorCode() == null ? "0000" : String.format("%04d", getOperatorCode()))
                .append(formatNames(getFirstname(), getLastname()));

        return builder.toString();
    }

    String toUpdateRecord() {
        return new StringBuilder(100).append(UPDATE)
                .append(String.format(ID_FORMAT, getId()))
                .append(getNiss())
                .append(getOperatorCode() == null ? "0000" : String.format("%04d", getOperatorCode()))
                .append(getValueDate().toString().replace("-", ""))
                .append(formatAddress(getAddress()))
                .append(getAddress().getZip())
                .append(getUnionDue() == null ? ' ' : (getUnionDue() ? '1' : '0'))
                .append(toRecord(getPaymentType()))
                .append(' ')
                .append(getNationalityCode() == null ? "   " : getNationalityCode())
                .toString();
    }

    static StringBuilder formatNames(String firstname, String lastname) {
        StringBuilder builder = new StringBuilder(50)
                .append(toUpper(lastname, MAX_LAST_NAME_SIZE))
                .append(',');

        int spaceLeft = NAMES_LENGTH - builder.length();
        builder.append(toUpper(firstname, spaceLeft));

        while (builder.length() < NAMES_LENGTH) {
            builder.append(' ');
        }

        return builder;
    }

    static String toUpper(String text, int maxLength) {
        var trimmed = text.trim();
        var formatted = trimmed.length() > maxLength ? trimmed.substring(0, maxLength) : trimmed;
        return StringUtils.stripAccents(formatted.toUpperCase(Locale.ROOT));
    }

    static String formatAddress(Address address) {
        String street = address.getStreet().trim();
        String nbr = address.getNumber() == null ? "" : ' ' + address.getNumber().trim().toUpperCase(Locale.ROOT);
        String box = address.getBox() == null ? ""
                : ' ' + address.getBox().trim().toUpperCase(Locale.ROOT).replace("BOITE", "BTE");

        int max = MAX_ADDRESS_SIZE - nbr.length() - box.length();

        String addressLine = toUpper(street, max) + nbr + box;
        int size = addressLine.length();

        return size == MAX_ADDRESS_SIZE ? addressLine : addressLine + " ".repeat(MAX_ADDRESS_SIZE - size);
    }

    public static char toRecord(PaymentType paymentType) {
        if (paymentType == null) {
            return ' ';
        }
        return switch (paymentType) {
            case BANK_TRANSFER -> '1';
            case OTHER_BANK_TRANSFER -> '2';
            case CIRCULAR_CHEQUE -> '3';
        };
    }

    public static class PersonRequestBuilder {
        private String firstname;
        private String lastname;

        public PersonRequestBuilder firstname(String name) {
            firstname = name.trim();
            return this;
        }

        public PersonRequestBuilder lastname(String name) {
            lastname = name.trim();
            return this;
        }

    }
}
