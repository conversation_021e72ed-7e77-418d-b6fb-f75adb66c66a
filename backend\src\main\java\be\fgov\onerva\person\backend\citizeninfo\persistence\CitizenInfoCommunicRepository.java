package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoCommunic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CitizenInfoCommunicRepository  extends JpaRepository<CitizenInfoCommunic, String>, JpaSpecificationExecutor<CitizenInfoCommunic> {

    List<CitizenInfoCommunic> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox, int flag, int dateValid);

}
