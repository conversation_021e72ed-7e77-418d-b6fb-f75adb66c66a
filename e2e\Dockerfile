FROM docker-release.onemrva.priv/onemrva/karate-e2e-image:4.0.18_27-3adfe89

ENV KARATE_ENV=dev

ENV APP_NAME=person

COPY e2e /project/
RUN curl https://bitbucket.onemrva.priv/projects/ARCHI/repos/workstation/raw/installation/files/settings.xml --output /project/settings.xml
RUN chmod -R a+wr /project/
RUN chmod ug=rx /project/e2e.sh
WORKDIR /project
ENV MAVEN_ARGS="-s settings.xml"

ENTRYPOINT [ "/project/e2e.sh" ]
