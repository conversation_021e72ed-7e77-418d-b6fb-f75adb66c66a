<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 https://maven.apache.org/xsd/settings-1.2.0.xsd">

    <proxies>
        <proxy>
            <id>insecure-proxygate</id>
            <active>true</active>
            <protocol>http</protocol>
            <host>proxygate</host>
            <port>8888</port>
            <nonProxyHosts>*.onemrva.priv|sonarqubeprod|nexusprod</nonProxyHosts>
        </proxy>
        <proxy>
            <id>secure-proxygate</id>
            <active>true</active>
            <protocol>https</protocol>
            <host>proxygate</host>
            <port>8888</port>
            <nonProxyHosts>*.onemrva.priv|sonarqubeprod|nexusprod</nonProxyHosts>
        </proxy>
    </proxies>

    <mirrors>
        <mirror>
            <id>nexus</id>
            <url>http://nexusprod.onemrva.priv/repository/maven-public/</url>
            <mirrorOf>!internal-releases,!internal-snapshots,external:*</mirrorOf>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>nexus</id>
            <repositories>
                <repository>
                    <id>internal-releases</id>
                    <url>http://nexusprod.onemrva.priv/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>internal-snapshots</id>
                    <url>http://nexusprod.onemrva.priv/repository/maven-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>interval:15</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>internal-releases</id>
                    <url>http://nexusprod.onemrva.priv/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
                <pluginRepository>
                    <id>internal-snapshots</id>
                    <url>http://nexusprod.onemrva.priv/repository/maven-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>interval:15</updatePolicy>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <activeProfiles>
        <activeProfile>nexus</activeProfile>
    </activeProfiles>
</settings>