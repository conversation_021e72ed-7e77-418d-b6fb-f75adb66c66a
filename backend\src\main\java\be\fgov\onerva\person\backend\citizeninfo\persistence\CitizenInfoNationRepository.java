package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoNation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CitizenInfoNationRepository  extends JpaRepository<CitizenInfoNation, String>, JpaSpecificationExecutor<CitizenInfoNation> {

    List<CitizenInfoNation> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox,int flag, int dateValid);
}
