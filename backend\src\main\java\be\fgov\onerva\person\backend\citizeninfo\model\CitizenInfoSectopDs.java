package be.fgov.onerva.person.backend.citizeninfo.model;


import jakarta.persistence.Column;
import lombok.*;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "sectop_ds", schema = "dbo")
public class CitizenInfoSectopDs {

    @Id
    private long id;

    private int numBox;

    private int flagValid;

    private int dateValid;

    private Integer sectOp;

    @Column(name= "mandat_syndic")
    private String unionDue;

    public boolean hasUnionDue() {
        return "1".equals(unionDue);
    }
}
