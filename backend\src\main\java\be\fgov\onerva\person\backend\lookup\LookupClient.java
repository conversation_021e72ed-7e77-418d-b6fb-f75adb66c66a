package be.fgov.onerva.person.backend.lookup;

import be.fgov.onerva.person.backend.lookup.model.BelgianCommunity;
import be.fgov.onerva.person.backend.lookup.model.LookupData;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClient;

import java.util.List;


@Component
@Slf4j
@Generated
public class LookupClient {

    private static final ParameterizedTypeReference<List<LookupData>> LOOKUP_LIST = new ParameterizedTypeReference<>() {};
    private static final ParameterizedTypeReference<List<BelgianCommunity>> ZIP_LIST = new ParameterizedTypeReference<>() {};

    private final RestClient rest;

    public LookupClient(
            RestClient.Builder builder,
            @Value("${lookup.url:https://services.onemrva.priv/lookupwpptservice/rest}") String url
    ) {
        rest = builder.baseUrl(url).build();
    }

    public List<LookupData> findAllNationalityCodes() {
        return rest.get()
                .uri("/lookup/getLookups?class=be.fgov.onerva.lookup.wppt.persistence.model.common.NationalityBCSS")
                .retrieve()
                .body(LOOKUP_LIST);
    }

    public List<LookupData> findAllOnemCountryCodes() {
        return rest.get()
                .uri("/lookup/getLookups?class=be.fgov.onerva.lookup.wppt.persistence.model.common.Country")
                .retrieve()
                .body(LOOKUP_LIST);
    }

    public List<BelgianCommunity> findBelgianZipCode(int zipCode) {
        try {
            return rest.get()
                    .uri("/lookup/getBelgianCommunityByPostalCode?postalCode={zip}", zipCode)
                    .retrieve()
                    .body(ZIP_LIST);
        } catch (HttpClientErrorException.NotFound e) {
            return List.of();
        }
    }

}
