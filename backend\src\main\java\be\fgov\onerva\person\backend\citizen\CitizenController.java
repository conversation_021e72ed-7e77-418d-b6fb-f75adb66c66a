package be.fgov.onerva.person.backend.citizen;


import backend.api.CitizenApi;
import backend.rest.model.CitizenCreationRequestDTO;
import backend.rest.model.CitizenDTO;
import backend.rest.model.CitizenPageDTO;
import backend.rest.model.CitizenUpdateRequestDTO;
import be.fgov.onerva.person.backend.citizen.mapper.CitizenMapper;
import be.fgov.onerva.person.backend.citizen.service.CitizenService;
import be.fgov.onerva.person.backend.request.PersonRequestController;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.MvcUriComponentsBuilder;

import java.net.URI;
import java.util.regex.Pattern;

@Slf4j
@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api")
@RequiredArgsConstructor
public class CitizenController implements CitizenApi {

    private static final Pattern NUMERIC = Pattern.compile("\\d+");

    private final CitizenService citizenService;
    private CitizenMapper citizenMapper = Mappers.getMapper(CitizenMapper.class);

    @Override
    public ResponseEntity<CitizenDTO> getByNiss(String niss) {
        isNumeric(niss);
        var citizenEntity = citizenService.getByNiss(niss);
        var citizenDto = citizenMapper.map(citizenEntity);
        return ResponseEntity.ok(citizenDto);
    }

    void isNumeric (String input) {
        if (input == null || !NUMERIC.matcher(input).matches()) {
            throw new ResponseStatusException(HttpStatusCode.valueOf(400), "Inss not numeric: " + input);
        }
    }

    @Override
    public ResponseEntity<CitizenDTO> getByNumbox(Integer numbox) {
        return ResponseEntity.ok(
                citizenMapper.map(
                        citizenService.getByNumbox(numbox)
                )
        );
    }

    @Override
    public ResponseEntity<CitizenPageDTO> searchCitizen(String query, Integer pageNumber, Integer pageSize) {
        var citizenPage = citizenService.searchCitizenByQuery(query, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, "numBr", "fullName")));
        var citizenPageDto = citizenMapper.mapPageToDto(citizenPage);
        return ResponseEntity.ok(citizenPageDto);
    }

    @Override
    public ResponseEntity<Void> createCitizen(String businessDomain, Boolean allowance, CitizenCreationRequestDTO citizenDTO) {
        if (citizenDTO.getFallbackUrl() != null) {
            throw new IllegalArgumentException("Fallback URL is no longer supported! Use messaging for response.");
        }
        var creationRequest = citizenMapper.map(citizenDTO, businessDomain, allowance);
        PersonRequest personRequest = citizenService.createCitizen(creationRequest);

        return ResponseEntity.created(getLink(personRequest)).build();
    }

    @Override
    public ResponseEntity<Void> updateCitizen(String niss, String username, CitizenUpdateRequestDTO citizenUpdateRequestDTO) {
        var updateRequest = citizenMapper.map(citizenUpdateRequestDTO, niss, username);
        PersonRequest personRequest = citizenService.updateCitizen(updateRequest);

        return ResponseEntity.noContent().location(getLink(personRequest)).build();
    }

    URI getLink(PersonRequest personRequest) {
        return MvcUriComponentsBuilder.fromController(PersonRequestController.class).path("/citizen/requests/{id}").build(personRequest.getId());
    }
}
