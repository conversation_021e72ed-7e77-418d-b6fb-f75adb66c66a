
create table PERSON_REQUEST (
    id          BIGINT NOT NULL IDENTITY,
    created     D<PERSON><PERSON>IME2 NOT NULL,
    niss        CHAR(11) NOT NULL,
    firstname   VA<PERSON>HAR(60) NOT NULL,
    lastname    VA<PERSON><PERSON><PERSON>(60) NOT NULL,
    sent        BIT NOT NULL,
    retry_count TINYINT NOT NULL,
    updated     DATETIME2,
    return_code SMALLINT,
    error       VARCHAR(2000),
    primary key (ID)
);
