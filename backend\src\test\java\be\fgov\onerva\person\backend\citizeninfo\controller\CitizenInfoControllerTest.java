package be.fgov.onerva.person.backend.citizeninfo.controller;


import backend.rest.model.*;
import be.fgov.onerva.person.backend.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.test.context.jdbc.Sql;

import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Sql(value = "/scripts/init-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
class CitizenInfoControllerTest extends IntegrationTest {

    private static final String BASE_PATH = "/api/citizen/info";


    @Test
    void getInfoByInvalidSsins() throws Exception {
        var error = ProblemDetail.forStatusAndDetail(HttpStatus.BAD_REQUEST, "Given ssins are invalid [70070700740, 6-6-6]");
        error.setInstance(new URI(BASE_PATH));

        rest.get()
                .uri(BASE_PATH + "?ssins=70070700741&ssins=70070700740&ssins=6-6-6")
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST)
                .bodyJson()
                .convertTo(ProblemDetail.class)
                .isEqualTo(error);
    }

    @Test
    void getInfoBySsinsAndNumbox() throws Exception {
        var error = ProblemDetail.forStatusAndDetail(HttpStatus.BAD_REQUEST, "Choose parameter ssins or citizedId but not both.");
        error.setInstance(new URI(BASE_PATH));

        rest.get()
                .uri(BASE_PATH + "?ssins=70070700741&citizenId=123")
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST)
                .bodyJson()
                .convertTo(ProblemDetail.class)
                .isEqualTo(error);
    }

    @ParameterizedTest
    @CsvSource({"''", "?ssins=70070700741&pageNumber=0&pageSize=10", "?ssins=&citizenId="})
    void getInfoBySsinNotFound(String params) throws Exception {
        var page = new CitizenInfoPageDTO()
                .totalPage(0)
                .totalElements(0)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + params)
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @Test
    void getInfoBySsinOk() {
        var citizenInfoDTO = new CitizenInfoDTO()
                .numPens(new BigDecimal(999231117L))
                .lastName("LASTNAME2")
                .firstName("NAME2")
                .ssin("***********")
                .address("ADDRESS2                 256")
                .postalCode("2235")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("2                 256")
                        .zip("2235")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2024,1,5))
                )
                .numBox(new BigDecimal(48640))
                .OP(BigDecimal.valueOf(2350))
                .iban("****************")
                .bankAccount(new BankAccountDTO()
                        .iban("****************")
                        .bic("BBRUBEBB")
                        .validFrom(LocalDate.of(2004,06,29))
                )
                .paymentMode(1)
                .unemploymentOffice(BigDecimal.valueOf(33))
                .sex(SexDTO.m.name())
                .language(LanguageDTO.fr.name())
                .flagToPurge(FlagDTO.n.name())
                .flagNation(BigDecimal.valueOf(150))
                .lastModifDate(********)
                .deceasedDate(null)
                .employmentContract(2)
                .email("<EMAIL>")
                .emailReg("<EMAIL>");
        var page = new CitizenInfoPageDTO().addContentItem(citizenInfoDTO)
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + "?ssins=***********&pageNumber=0&pageSize=10")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @Test
    void getInfoBySsinWithBankAccountHistory() {
        var citizenInfoDTO = new CitizenInfoDTO()
                .numPens(new BigDecimal(*********))
                .lastName("LASTNAME1")
                .firstName("NAME1")
                .ssin("***********")
                .address("ADDRESS2 83/3")
                .postalCode("5342 TK")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRESS2")
                        .number("83")
                        .box("3")
                        .city("OSS")
                        .zip("5342 TK")
                        .countryCode(2)
                        .validFrom(LocalDate.of(2020,11,6))
                )
                .numBox(new BigDecimal(47000))
                .OP(BigDecimal.valueOf(2210))
                .iban("******************")
                .bankAccount(new BankAccountDTO()
                        .iban("******************")
                        .bic("RABONL20")
                        .validFrom(LocalDate.of(2020,6,29))
                )
                .paymentMode(1)
                .unemploymentOffice(BigDecimal.valueOf(21))
                .flagToPurge(FlagDTO.n.name())
                .flagNation(BigDecimal.valueOf(150))
                .lastModifDate(********)
                .deceasedDate(null)
                .sex("m")
                .employmentContract(2);
        var page = new CitizenInfoPageDTO().addContentItem(citizenInfoDTO)
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + "?ssins=***********")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @Test
    void getInfoBySsinNoBankAccount() {
        var citizenInfoDTO = new CitizenInfoDTO()
                .numPens(new BigDecimal(*********))
                .lastName("LASTNAME3")
                .firstName("NAME3")
                .ssin("***********")
                .language(LanguageDTO.fr.name())
                .address("ADDRESS3 26")
                .postalCode("3360")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("3 26")
                        .zip("3360")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2023,4,1))
                )
                .numBox(new BigDecimal(13))
                .OP(BigDecimal.valueOf(1221))
                .paymentMode(3)
                .unemploymentOffice(BigDecimal.valueOf(22))
                .flagToPurge(FlagDTO.n.name())
                .flagNation(BigDecimal.valueOf(150))
                .lastModifDate(********)
                .sex("f")
                .deceasedDate(null);
        var page = new CitizenInfoPageDTO().content(Collections.singletonList(citizenInfoDTO))
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + "?ssins=***********")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @Test
    void getInfoBySsinBankAccountFromGeneral() {
        var citizenInfoDTO = new CitizenInfoDTO()
                .numPens(new BigDecimal(*********))
                .lastName("LASTNAME4")
                .firstName("NAME4")
                .ssin("***********")
                .address("ADDRESS4 1073")
                .postalCode("1082")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("4 1073")
                        .zip("1082")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2022,5,5))
                )
                .numBox(new BigDecimal(15))
                .OP(BigDecimal.valueOf(3210))
                .iban("****************")
                .bankAccount(new BankAccountDTO()
                        .iban("****************")
                        .bic("VDSPBE91")
                        .validFrom(LocalDate.of(2004,6,8))
                )
                .unemploymentOffice(BigDecimal.valueOf(21))
                .flagToPurge(FlagDTO.y.name())
                .flagNation(BigDecimal.valueOf(150))
                .lastModifDate(********)
                .sex("m")
                .deceasedDate(null);
        var page = new CitizenInfoPageDTO().content(Collections.singletonList(citizenInfoDTO))
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + "?ssins=***********")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @Test
    void getInfoBySsinNoBankAccountFromGeneral() {
        var citizenInfoDTO = new CitizenInfoDTO()
                .numPens(new BigDecimal(*********))
                .lastName("LASTNAME5")
                .firstName("NAME5")
                .ssin("***********")
                .address("ADDRESS7 BTE A")
                .postalCode("1082")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("7")
                        .box("A")
                        .zip("1082")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2023,6,6))
                )
                .numBox(new BigDecimal(38448))
                .OP(BigDecimal.valueOf(3210))
                .unemploymentOffice(BigDecimal.valueOf(21))
                .flagToPurge(FlagDTO.y.name())
                .flagNation(BigDecimal.valueOf(150))
                .lastModifDate(********)
                .sex("m")
                .unionDue(new CitizenInfoUnionDueDTO(true, LocalDate.of(2011,12,29)))
                .deceasedDate(null);
        var page = new CitizenInfoPageDTO().content(Collections.singletonList(citizenInfoDTO))
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH + "?ssins=***********")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .isEqualTo(page);
    }

    @ParameterizedTest
    @CsvSource({"?ssins=99051435083&ssins=83101625412&ssins=***********", "?citizenId=974&citizenId=30536"})
    void getCitizensWithInactiveSsinAndCitizenIds(String params) {
        var citizen1 = new CitizenInfoDTO()
                .numPens(new BigDecimal(998514350))
                .lastName("LASTNAME6")
                .firstName("NAME6")
                .ssin("99051435083")
                .address("ADDRESS5B29")
                .postalCode("2020")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("5")
                        .box("29")
                        .zip("2020")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2005,11,21))
                )
                .numBox(new BigDecimal(974))
                .OP(BigDecimal.valueOf(3111))
                .unemploymentOffice(BigDecimal.valueOf(11))
                .flagToPurge(FlagDTO.y.name())
                .flagNation(BigDecimal.valueOf(150))
                .employmentContract(2)
                .language("nl")
                .lastModifDate(********)
                .sex("f")
                .iban("****************")
                .bankAccount(new BankAccountDTO()
                        .iban("****************")
                        .bic("GKCCBEBB")
                        .validFrom(LocalDate.of(2004,1,14))
                )
                .bisNumber(List.of( "***********", "***********"))
                .gsmReg("**********")
                .unionDue(new CitizenInfoUnionDueDTO(true, LocalDate.of(2005,9,1)))
                .deceasedDate(null);
        var citizen2 = new CitizenInfoDTO()
                .numPens(new BigDecimal(*********))
                .lastName("LASTNAME7")
                .firstName("NAME7")
                .ssin("***********")
                .address("ADDRESS6, 6")
                .postalCode("1430")
                .addressObj(new ForeignAddressDTO()
                        .street("ADDRES")
                        .number("6, 6")
                        .zip("1430")
                        .countryCode(1)
                        .validFrom(LocalDate.of(2003,8,20))
                )
                .numBox(new BigDecimal(30536))
                .OP(BigDecimal.valueOf(2920))
                .unemploymentOffice(BigDecimal.valueOf(23))
                .flagToPurge(FlagDTO.y.name())
                .flagNation(BigDecimal.valueOf(150))
                .employmentContract(2)
                .language("fr")
                .lastModifDate(********)
                .sex("m")
                .iban("****************")
                .bankAccount(new BankAccountDTO()
                        .iban("****************")
                        .bic("GKCCBEBB")
                        .validFrom(LocalDate.of(1995,2,1))
                )
                .bisNumber(List.of("***********", "***********"))
                .telephoneReg("*********");

        rest.get()
                .uri(BASE_PATH + params)
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenInfoPageDTO.class)
                .satisfies(body -> {
                    assertThat(body.getTotalElements()).isEqualTo(2);
                    assertThat(body.getContent()).containsExactlyInAnyOrder(citizen1, citizen2);
                });
    }

}