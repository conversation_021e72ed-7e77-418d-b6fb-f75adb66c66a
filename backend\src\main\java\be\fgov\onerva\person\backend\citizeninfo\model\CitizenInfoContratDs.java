package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "contrat_ds", schema = "dbo")
public class CitizenInfoContratDs {

    @Id
    private long id;

    private int numBox;

    private int flagValid;

    private int dateValid;

    @Column(name = "contrat_trav")
    private Integer employmentContract;
}
