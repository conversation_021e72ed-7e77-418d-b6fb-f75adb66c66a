{{ if .Values.karate.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "person.fullname" . }}-karate-e2e"
  labels:
    {{- include "person.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  volumes:
    - name: target
      emptyDir: { }
    - name: repository
      emptyDir: { }
  containers:
    - name: karate-e2e
      image: "{{.Values.karate.image.registry}}/{{.Values.karate.image.repository}}:{{.Values.karate.image.tag}}"
      resources:
        requests:
          cpu: 100m
          memory: 1024Mi
        limits:
          cpu: 1000m
          memory: 1024Mi
      env:
        - name: KARATE_ENV
          value: {{.Values.karate.env}}
      volumeMounts:
        - mountPath: /usr/src/app/target
          name: target
        - mountPath: /usr/src/app/.m2/repository
          name: repository
  restartPolicy: Never
{{ end }}