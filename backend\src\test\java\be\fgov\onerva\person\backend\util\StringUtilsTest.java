package be.fgov.onerva.person.backend.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@DisplayName("StringUtils Tests")
class StringUtilsTest {

    @Test
    @DisplayName("Should truncate and pad right with spaces")
    void truncateAndPadRight_shouldTruncateAndPadCorrectly() {
        // Given
        String input = "Hello World";
        int length = 15;

        // When
        String result = StringUtils.truncateAndPadRight(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("Hello World    ");
    }

    @Test
    @DisplayName("Should truncate long string when padding right")
    void truncateAndPadRight_withLongString_shouldTruncate() {
        // Given
        String input = "This is a very long string that exceeds the limit";
        int length = 10;

        // When
        String result = StringUtils.truncateAndPadRight(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("This is a ");
    }

    @Test
    @DisplayName("Should handle null input when padding right")
    void truncateAndPadRight_withNullInput_shouldReturnSpaces() {
        // Given
        String input = null;
        int length = 5;

        // When
        String result = StringUtils.truncateAndPadRight(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("     ");
    }

    @Test
    @DisplayName("Should truncate and pad left with spaces")
    void truncateAndPadLeft_shouldTruncateAndPadCorrectly() {
        // Given
        String input = "123";
        int length = 5;

        // When
        String result = StringUtils.truncateAndPadLeft(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("  123");
    }

    @Test
    @DisplayName("Should truncate and pad left with custom character")
    void truncateAndPadLeft_withCustomCharacter_shouldPadCorrectly() {
        // Given
        String input = "42";
        int length = 5;
        char character = '0';

        // When
        String result = StringUtils.truncateAndPadLeft(input, length, character);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("00042");
    }

    @Test
    @DisplayName("Should handle null input when padding left with custom character")
    void truncateAndPadLeft_withNullInputAndCustomCharacter_shouldReturnPaddedString() {
        // Given
        String input = null;
        int length = 3;
        char character = 'X';

        // When
        String result = StringUtils.truncateAndPadLeft(input, length, character);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("XXX");
    }

    @Test
    @DisplayName("Should convert to uppercase and clean accents")
    void toUpperClean_shouldConvertAndCleanCorrectly() {
        // Given
        String input = "Café à Paris";
        int maxLength = 20;

        // When
        String result = StringUtils.toUpperClean(input, maxLength);

        // Then
        assertThat(result).isEqualTo("CAFE A PARIS");
    }

    @Test
    @DisplayName("Should handle special French/Dutch replacements")
    void toUpperClean_withSpecialCases_shouldReplaceCorrectly() {
        // Given
        String input = "Boîte 123";
        int maxLength = 20;

        // When
        String result = StringUtils.toUpperClean(input, maxLength);

        // Then
        assertThat(result).isEqualTo("BTE 123");
    }

    @Test
    @DisplayName("Should truncate when text exceeds max length")
    void toUpperClean_withLongText_shouldTruncate() {
        // Given
        String input = "This is a very long text that should be truncated";
        int maxLength = 10;

        // When
        String result = StringUtils.toUpperClean(input, maxLength);

        // Then
        assertThat(result).hasSize(maxLength);
        assertThat(result).isEqualTo("THIS IS A ");
    }

    @Test
    @DisplayName("Should handle null input in toUpperClean")
    void toUpperClean_withNullInput_shouldReturnEmptyString() {
        // Given
        String input = null;
        int maxLength = 10;

        // When
        String result = StringUtils.toUpperClean(input, maxLength);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should remove non-ASCII characters")
    void toUpperClean_withNonAsciiCharacters_shouldRemoveThem() {
        // Given
        String input = "Test 中文 ñ";
        int maxLength = 20;

        // When
        String result = StringUtils.toUpperClean(input, maxLength);

        // Then
        assertThat(result).isEqualTo("TEST  N");
    }

    @Test
    @DisplayName("Should handle empty string")
    void truncateAndPadRight_withEmptyString_shouldReturnSpaces() {
        // Given
        String input = "";
        int length = 3;

        // When
        String result = StringUtils.truncateAndPadRight(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("   ");
    }

    @Test
    @DisplayName("Should handle exact length string")
    void truncateAndPadRight_withExactLength_shouldReturnSameString() {
        // Given
        String input = "EXACT";
        int length = 5;

        // When
        String result = StringUtils.truncateAndPadRight(input, length);

        // Then
        assertThat(result).hasSize(length);
        assertThat(result).isEqualTo("EXACT");
    }
}
