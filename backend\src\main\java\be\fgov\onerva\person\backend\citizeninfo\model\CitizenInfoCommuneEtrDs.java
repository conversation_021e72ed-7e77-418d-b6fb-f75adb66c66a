package be.fgov.onerva.person.backend.citizeninfo.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "commune_etr_ds", schema = "dbo")
public class CitizenInfoCommuneEtrDs {
    @Id
    private long id;

    private int dateValid;

    private int numBox;

    private int flagValid;

    @Column(name = "rue")
    private String street;

    @Column(name = "numero")
    private String number;

    @Column(name = "code_post")
    private String zip;

    @Column(name = "commune")
    private String city;

}
