truncate table nation_ds;
truncate table communic_ds;
truncate table cpte_ds;
truncate table commune_ds;
truncate table siggen_ds;
truncate table sectop_ds;
truncate table prof_ds;
truncate table contrat_ds;
truncate table keybox_ds;
truncate table commune_etr_ds;
delete from general_ds;


-- SET IDENTITY_INSERT keybox_ds ON;
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (999231999, N'LASTNAME1,NAME1            ', N'LNAM1  ', N'NAM1', 47000, 2, 998508, 1180, 9, 21, 0, 1, 0, 0, 0, 0);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (999231117, N'LASTNAME2,NAME2       ', N'LNAM2  ', N'NAM2', 48640, 1, 998117, 2235, 9, 13, 0, 1, 0, 0, 0, 0);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (999213078, N'LASTNAME3,NAME3          ', N'LNAM3 ', N'NAM3', 13, 2, 998513, 8620, 9, 35, 0, 1, 0, 0, 0, 0);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (999219079, N'LASTNAME4,NAME4              ', N'LNAM4 ', N'NAM4', 15, 1, 999219, 5171, 9, 92, 1, 1, 1, 0, 0, 0);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998624049, N'LASTNAME5,NAME5                 ', N'LNAME5', N'NAM5', 38448, 1, 998624, 7270, 9, 53, 1, 1, 1, 0, 0, 0);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998605063, N'LASTNAME5,NAME5', N'LNAME5', N'NAM5', 11390, 1, 998624, 7270, 9, 53, 1, 1, 1, 0, 0, 0);

INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998514350, 'LASTNAME6,NAME6                 ', 'LNME6 ', 'NAM6', 974,   2, 998514, 2020, 9, 11, 'true', 'true', 'true',  'false', 'false', 1);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998514360, 'LASTNAME6,NAME6                 ', 'LNME6 ', 'NAM6', 974,   2, 998514, 2020, 1, 11, 'true', 'true', 'false', 'false', 'false', 1);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (999016254, 'LASTNAME6,NAME6                 ', 'LNME6 ', 'NAM6', 974,   2, 998514, 2020, 1, 11, 'true', 'true', 'false', 'false', 'false', 1);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998522537, 'LASTNAME7,NAME7         ', 'LNAME7', 'NAM7', 30536, 1, 998522, 1430, 9, 23, 'true', 'true', 'true',  'false', 'false', 1);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (998520467, 'LASTNAME7,NAME7         ', 'LNAME7', 'NAM7', 30536, 1, 998522, 1430, 1, 23, 'true', 'true', 'false', 'false', 'false', 1);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_personnel, source_id) VALUES (992520008, 'LASTNAME7,NAME7         ', 'LNAME7', 'NAM7', 30536, 1, 998522, 1430, 1, 23, 'true', 'true', 'false', 'false', 'false', 1);
-- SET IDENTITY_INSERT keybox_ds OFF;


INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, langue_gn, sect_op, num_br, cpte_iban_gn, date_deces_car_gn,flag_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen) VALUES (1,  N'LASTNAME2,NAME2 ', N'ADDRESS1,28               ', 48640, 4700,63023,1,1,1,2350,33,77775320570,99999999,0,20050929,20200202,0);
INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, sect_op, num_br, cpte_iban_gn, date_deces_car_gn,flag_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen,mode_pay_gn) VALUES (2, N'LASTNAME1,NAME1           ', 'ADDRESS2 83       ', 47000, 0,2, 0, 1, 2210, 21, 'BE324836485274', 99999999,0,20050929,20240505,1, '1');
INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, sect_op, num_br, cpte_iban_gn, date_deces_car_gn,flag_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen) VALUES (3, N'LASTNAME3,NAME3                 ', N'ADDRESS3 26                ', 13, 3360,24011, 0, 0,1221, 22, 'BE324836485274', 99999999,0,20050929,20210505,0);
INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, sect_op, num_br, cpte_iban_gn, cpte_bic_gn, date_deces_car_gn,flag_v_cpte,date_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen) VALUES (4, N'LASTNAME4,NAME4                 ', N'ADDRESS4 1073         ', 15, 1082,21003, 0, 1, 3210, 21, '****************','VDSPBE91   ', 99999999,1,20040608, 20050929,20220505,1);
INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, sect_op, num_br, date_deces_car_gn,flag_v_cpte,date_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen,mandat_syndic) VALUES (5, N'LASTNAME5,NAME5                 ', N'ADDRESS4 1073         ', 38448, 1082,21003, 0, 1, 3210, 21,  99999999,1,20050929,20050929,20140505,1, '1');
INSERT INTO general_ds (id, nom_prenom_gn, adresse, num_box, code_post, code_resid, flag_nation, sexe_gn, sect_op, num_br, date_deces_car_gn,flag_v_cpte,date_v_cpte,date_m_cpte,date_v_siggen,flag_v_siggen) VALUES (6, N'LASTNAME5,NAME5                 ', N'ADDRESS4 1073         ', 11390, 1082,21003, 0, 1, 3210, 21,  99999999,1,20050929,20050929,20040505,1);
INSERT INTO general_ds (cpte_tit_gn, nom_prenom_gn, adresse, num_box, date_v_siggen, date_v_cpte, date_m_cpte, langue_gn, num_br, code_post, code_resid, sect_op, flag_v_siggen, flag_v_cpte, flag_nation, sexe_gn, date_deces_car_gn, cpte_iban_gn, cpte_bic_gn, id) VALUES ('                    ', 'LASTNAME6,NAME6                 ', 'ADDRESS5 51 - B32   ', 974,  20051121,  20040114,  20090206, 2, 11, 2020,34922,3111, 'true',  'true', 'true', 0, 99999999, '****************                  ', 'GKCCBEBB   ', 299);
INSERT INTO general_ds (cpte_tit_gn, nom_prenom_gn, adresse, num_box, date_v_siggen, date_v_cpte, date_m_cpte, langue_gn, num_br, code_post, code_resid, sect_op, flag_v_siggen, flag_v_cpte, flag_nation, sexe_gn, date_deces_car_gn, cpte_iban_gn, cpte_bic_gn, id) VALUES ('                    ', 'LASTNAME7,NAME7         ', 'ADDRESS6, 6           ', 30536, 20100222,  19950201,  20081003, 1, 23, 1430,34040,2920, 'true', 'true', 'true', 1, 99999999, '****************                  ', 'GKCCBEBB   ', 13181);


SET IDENTITY_INSERT nation_ds ON;
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_aa,my_id)VALUES (48640, 5828, 9, 20040629, 20040629, 150, 20050929, 2258, 150, 0, 1, 1, 1,1);
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_aa,my_id)VALUES (11390, 5828, 9, 20050629, 20050629, 150, 20050929, 2258, 150, 0, 2, 2, 2,2);
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (974, 0, 9, 20101011, 20111206, 1, 0, 0, 150, 0, 1, 6498, 6498, '6498        ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (974, 0, 1, 20101011, 20111129, 1, 20111206, 0, 150, 0, 1, 4786, 4786, '4786        ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (974, 2471, 9, 20040114, 20040120, 28, 0, 0, 0, 0, 1, 11, 11, '11          ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (30536, 0, 9, 20030820, 20111208, 1, 0, 0, 150, 0, 1, 9765, 9765, '9765        ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (30536, 5450, 1, 20030820, 20031002, 1, 20111208, 0, 0, 0, 1, 9764, 9764, '9764        ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (30536, 5317, 9, 19920729, 19950428, 41, 0, 0, 0, 0, 1, 9763, 9763, '9763        ');
INSERT INTO nation_ds (num_box, code_operateur, flag_valid, date_valid, date_modif, nation, date_modif_f4, code_operateur_f4, nation_bcss, refugie, source_id, id, my_id, my_aa) VALUES (30536, 5317, 1, 19920729, 19950428, 1, 0, 0, 0, 0, 1, 9762, 9762, '9762        ');
SET IDENTITY_INSERT nation_ds OFF;

SET IDENTITY_INSERT communic_ds ON;
INSERT INTO communic_ds (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4,
                         tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem, date_modif_reg, date_modif_onem, e_box, date_modif_e_box,
                         e_box_active, paperless, email_onem_2, source_id, id, my_aa,my_id)VALUES (48640, 20060123, 9, 20060124, 63, 20060124,
                                                                                                   63, '0476616295', '0496/021495'
                                                                                                  , '<EMAIL>'
                                                                                                  , '015286443', '015286443'
                                                                                                  , '<EMAIL>'
                                                                                                  , 0, 0, N' ', 0, N' ', N' '
                                                                                                  , N'                                                  '
                                                                                                  , 1, 1, 1,1);
INSERT INTO communic_ds (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4,
                         tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem, date_modif_reg, date_modif_onem, e_box, date_modif_e_box,
                         e_box_active, paperless, email_onem_2, source_id, id, my_aa,my_id)VALUES (11390, 20060123, 9, 20060124, 63, 20060124,
                                                                                                   63, '0476616295', '0496/021495'
                                                                                                  , '<EMAIL>'
                                                                                                  , '015286443', '015286443'
                                                                                                  , '<EMAIL>'
                                                                                                  , 0, 0, N' ', 0, N' ', N' '
                                                                                                  , N'                                                  '
                                                                                                  , 2, 2, 2,2);
INSERT INTO communic_ds (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4, tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem, date_modif_reg, date_modif_onem, e_box, date_modif_e_box, e_box_active, paperless, email_onem_2, source_id, tel_reg_sanitized, gsm_reg_sanitized, tel_onem_sanitized, gsm_onem_sanitized, id, my_id, my_aa) VALUES (974, 20060318, 9, 20060318, 0, 0, 0, '                 ', '0485392611       ', '                                                  ', '                 ', '                 ', '                                                  ', 0, 0, ' ', 0, ' ', ' ', '                                                  ', 1, '', '0485392611', '', '', 90, 90, '90          ');
INSERT INTO communic_ds (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4, tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem, date_modif_reg, date_modif_onem, e_box, date_modif_e_box, e_box_active, paperless, email_onem_2, source_id, tel_reg_sanitized, gsm_reg_sanitized, tel_onem_sanitized, gsm_onem_sanitized, id, my_id, my_aa) VALUES (974, 20060318, 1, 20060318, 0, 20060318, 0, '                 ', '0485392611       ', '                                                  ', '                 ', '                 ', '                                                  ', 0, 0, ' ', 0, ' ', ' ', '                                                  ', 1, '', '0485392611', '', '', 94, 94, '94          ');
INSERT INTO communic_ds (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4, tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem, date_modif_reg, date_modif_onem, e_box, date_modif_e_box, e_box_active, paperless, email_onem_2, source_id, tel_reg_sanitized, gsm_reg_sanitized, tel_onem_sanitized, gsm_onem_sanitized, id, my_id, my_aa) VALUES (30536, 20070618, 9, 20070618, 0, 0, 0, '067/844031       ', '                 ', '                                                  ', '                 ', '                 ', '                                                  ', 0, 0, ' ', 0, ' ', ' ', '                                                  ', 1, '067844031', '', '', '', 24317, 24317, '24317       ');
SET IDENTITY_INSERT communic_ds OFF ;

SET IDENTITY_INSERT dbo.cpte_ds ON;
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_id)VALUES (N'                    ', 20040629, 9, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 8, 2, N'8           ', 1);
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_id)VALUES (N'                    ', 20040629, 9, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 9, 2, N'9           ', 2);
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_id)VALUES (N'                    ', 20200629, 9, 20090206, 20090206, 0, 0, 3300121083, N'******************                ', N'RABONL20   ', 1, 0, 10, 2, N'10           ', 2);
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_id)VALUES (N'                    ', 20040629, 1, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 11, 2, N'11           ', 4);
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 20040114, 1, 20040120, 20090206, 2471, 0, 632698232, '****************                  ', 'GKCCBEBB   ', 1, 0, 4302, 4302, 299, '4302        ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 20040114, 1, 20090206, 20090206, 0, 0, 632698232, '****************                  ', 'GKCCBEBB   ', 1, 0, 4301, 4301, 299, '4301        ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 20040114, 1, 20090206, 20090206, 0, 0, 632698232, '****************                  ', 'GKCCBEBB   ', 1, 0, 4300, 4300, 299, '4300        ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 20040114, 1, 20090206, 20090206, 0, 0, 632698232, '****************                  ', 'GKCCBEBB   ', 1, 0, 4299, 4299, 299, '4299        ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 20040114, 1, 20090206, 20090206, 0, 0, 632698232, '****************                  ', 'GKCCBEBB   ', 1, 0, 4298, 4298, 299, '4298        ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19950201, 1, 19950811, 20081003, 7159, 0, 639679393, '                                  ', '           ', 1, 0, 55371, 55371, 13181, '55371       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19930405, 9, 20081003, 0, 0, 0, 631788006, '****************                  ', 'GKCCBEBB   ', 1, 0, 55372, 55372, 13181, '55372       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19930405, 1, 19930429, 20081003, 6904, 0, 631788006, '                                  ', '           ', 1, 0, 55373, 55373, 13181, '55373       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19920729, 9, 20081003, 0, 0, 0, 639679393, '****************                  ', 'GKCCBEBB   ', 1, 0, 55374, 55374, 13181, '55374       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19920729, 1, 19950428, 20081003, 5317, 0, 639679393, '                                  ', '           ', 1, 0, 55375, 55375, 13181, '55375       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19880624, 9, 20081003, 0, 0, 0, 3100308061, '****************                  ', 'BBRUBEBB   ', 1, 0, 55376, 55376, 13181, '55376       ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, parent_id, my_rsn) VALUES ('                    ', 19880624, 1, 19900706, 20081003, 5042, 0, 3100308061, '                                  ', '           ', 1, 0, 55377, 55377, 13181, '55377       ');
SET IDENTITY_INSERT dbo.cpte_ds OFF;

INSERT INTO dbo.siggen_ds (date_valid, flag_valid, langue, mode_pay, id, parent_id) VALUES (20241204,9,1, 1, 10, 1);
INSERT INTO dbo.siggen_ds (date_valid, flag_valid, langue, mode_pay, id, parent_id) VALUES (20210505,9,1, 3, 11, 3);

--
INSERT INTO commune_ds (num_box, flag_valid, date_valid, code_resid, adresse, code_post) VALUES (48640,9,20240105,13016, 'ADDRESS2                 256', 2235);
INSERT INTO commune_ds (num_box, flag_valid, date_valid, code_resid, adresse, code_post) VALUES (47000,9,20240605,2, 'ADDRESS2 83/3        ', 0);
INSERT INTO commune_ds (num_box, flag_valid, date_valid, code_resid, adresse, code_post) VALUES (13,9,20230401,24011, 'ADDRESS3 26  ', 3360);
INSERT INTO commune_ds (num_box, flag_valid, date_valid, code_resid, adresse, code_post) VALUES (15,9,20220505,21003, 'ADDRESS4 1073         ', 1082);
INSERT INTO commune_ds (num_box, flag_valid, date_valid, code_resid, adresse, code_post) VALUES (38448,9,20230606,21003, 'ADDRESS7 BTE A       ', 1082);

INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS5B29     ', 974,   9, 20051121, 2020, 11002);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS5 B32        ', 974,   1, 20051121, 2020, 11002);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS8                   ', 974,   9, 20040114, 2660, 11002);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS6, 6           ', 30536, 9, 20030820, 1430, 25123);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS9,               ', 30536, 9, 19960301, 1081, 21011);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS9, 362           ', 30536, 1, 19901001, 1080, 21011);
INSERT INTO commune_ds (adresse, num_box, flag_valid, date_valid, code_post, code_resid) VALUES ('ADDRESS9,               ', 30536, 9, 19880624, 1080, 21011);


INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (48640,9,20240205, 2350, '0');
INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (47000,9,20240305, 2210, ' ');
INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (   13,9,20150409, 1221, '0');
INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (   15,9,20170731, 3210, '0');
INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (38448,9,20111229, 3210, '1');
INSERT INTO sectop_ds (num_box, flag_valid, date_valid, sect_op, mandat_syndic) VALUES (38448,9,20190909, 3210, '1');

INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (  974, 20040114, 9, 3111, '0');
INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (  974, 20050901, 9, 3111, '1');
INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (30536, 19880624, 1, 2210, ' ');
INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (30536, 19880624, 9, 2210, '0');
INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (30536, 20030820, 9, 2231, '0');
INSERT INTO sectop_ds (num_box, date_valid, flag_valid, sect_op, mandat_syndic) VALUES (30536, 20160825, 9, 2920, '0');


INSERT INTO prof_ds (num_box, flag_valid, date_valid) VALUES (48640,9,20240905);
INSERT INTO prof_ds (num_box, flag_valid, date_valid) VALUES (47000,9,20240705);

INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (48640,9, 20200317, 2);
INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (48640,1, 20200317, 1);
INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (48640,9, 19790108, 2);
INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (47000,9, 20241005, 2);
INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (974, 9, 20040114, 2);
INSERT INTO contrat_ds (num_box, flag_valid, date_valid, contrat_trav) VALUES (30536, 9, 19880624, 2);

INSERT INTO commune_etr_ds (num_box, date_valid, flag_valid, rue, numero, code_post, commune, pays, id) VALUES (47000, 20201106, 9, 'ADDRESS2                ', '83/3       ', '5342 TK   ', 'OSS                                ', 'NEDERLAND           ', 1);

