package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "nation_ds", schema = "dbo")
public class CitizenInfoNation {

    @Id
    private long id;

    private int numBox;

    @Column(name="nation_bcss")
    private Integer nation;

    private int flagValid;

    private int dateValid;
}
