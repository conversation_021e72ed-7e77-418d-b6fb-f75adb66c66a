apiVersion: "v2"
name: "person"
description: "A Helm chart for Person"
type: "application"
version: "0.2.1"

dependencies:
  - name: spring-boot
    alias: backend
    version: 3.8.0
    condition: backend.enabled
    repository: https://nexusprod.onemrva.priv/repository/helm-release
  - name: keycloak-config
    alias: kcconfig
    version: 31.0.0
    condition: kcconfig.enabled
    repository: https://nexusprod.onemrva.priv/repository/helm-release
  - name: fake-onem-infra
    alias: infra
    version: 5.0.0
    condition: infra.enabled
    repository: https://nexusprod.onemrva.priv/repository/helm-release
  - name: mssql-linux
    alias: db
    version: 1.0.0-2019.CU12.rhel.8.3.de29074
    condition: infra.enabled
    repository: https://nexusprod.onemrva.priv/repository/helm