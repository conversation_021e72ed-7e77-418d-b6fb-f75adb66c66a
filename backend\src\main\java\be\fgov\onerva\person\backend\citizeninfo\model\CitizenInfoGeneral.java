package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "general_ds", schema = "dbo")
public class CitizenInfoGeneral {
    @Id
    private long id;

    private int numBox;

    @Column(name = "nom_prenom_gn")
    private String fullName;

    @Column(name = "adresse")
    private String address;

    @Column(name = "code_post")
    private Integer zipCode;

    private Integer codeResid;

    @Column(name = "sect_op")
    private Integer OP;

    @Column(name = "num_br")
    private Integer unemploymentOffice;

    @Column(name = "cpte_iban_gn")
    private String iban;

    @Column(name = "cpte_bic_gn")
    private String bic;

    @Column(name = "cpte_tit_gn")
    private String bankAccountHolder;

    @Column(name = "mode_pay_gn")
    private Integer paymentMode;

    @Column(name = "flag_v_cpte")
    private boolean flagVCpte;

    @Column(name = "date_m_cpte")
    private Integer dateMCpte;

    @Column(name = "date_v_cpte")
    private Integer dateVCpte;

    @Column(name = "date_naiss_gn")
    private Integer birthDate;

    @Column(name = "date_deces_car_gn")
    private Integer deceasedDate;

    @Column(name = "langue_gn")
    private Integer language;

    @Column(name = "sexe_gn")
    private int sex;

    private boolean flagNation;

    @Column(name = "date_v_siggen")
    private Integer dateVSiggen;

    @Column(name= "flag_v_siggen")
    private boolean flagSiggen;

    @Column(name= "mandat_syndic")
    private String  unionDue;

}
