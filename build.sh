#!/usr/bin/env bash

## Configure bash
# Force abort on errors
set -e

## Change to correct directory
pushd "$(dirname -- "${BASH_SOURCE[0]}")" > /dev/null
trap "popd >/dev/null" TERM EXIT

# source common libraries
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/toolsrc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/toolsrc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/xwikirc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/xwikirc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/apicurio-clirc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/apicurio-clirc)
# git versioning
source <(curl -s -S https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/gitversionrc?at=refs%2Fheads%2Fmaster)

ProgName=$(basename "$0")

profile_test(){
  echo "Profile test activated"
  # shellcheck disable=SC2154 # Provided by Bamboo
  DEPLOY_HELM_ARGS=" --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.secrets.datasource\.mfx\.password=${mfx_datasource_password}\
   --set backend.secrets.datasource\.person\.password=${person_datasource_password}\
   --set backend.secrets.ibm\.mq\.password=${ibm_mq_password}\
   --set backend.secrets.rabbitmq\.password=${rabbitmq_password}\
   --set kcconfig.realm.clients.person-backend.secret=${rabbitmq_password}\
   --set backend.secrets.flagsmith\.environment\.id=${flagsmith_environment_id}"
  DEPLOY_ENV="test"
}

profile_val(){
  echo "Profile val activated"
  DEPLOY_HELM_ARGS=" --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.secrets.datasource\.mfx\.password=${mfx_datasource_password}\
   --set backend.secrets.datasource\.person\.password=${person_datasource_password}\
   --set backend.secrets.ibm\.mq\.password=${ibm_mq_password}\
   --set backend.secrets.rabbitmq\.password=${rabbitmq_password}\
   --set kcconfig.realm.clients.person-backend.secret=${rabbitmq_password}\
   --set backend.secrets.flagsmith\.environment\.id=${flagsmith_environment_id}"
  DEPLOY_ENV="val"
}

profile_prod(){
  DEPLOY_HELM_ARGS=" --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.secrets.datasource\.mfx\.password=${mfx_datasource_password}\
   --set backend.secrets.datasource\.person\.password=${person_datasource_password}\
   --set backend.secrets.ibm\.mq\.password=${ibm_mq_password}\
   --set backend.secrets.rabbitmq\.password=${rabbitmq_password}\
   --set kcconfig.realm.clients.person-backend.secret=${rabbitmq_password}\
   --set backend.secrets.flagsmith\.environment\.id=${flagsmith_environment_id}"
  DEPLOY_ENV="prod"
}

profile_ci(){
  echo "Profile ci activated"
  ns_uid=$(tools__get_first_uid "$(tools__kubecontext_for ci v4)" "$(openshift_namespace $HELM_CHART_NAME ci)")
  SKAFFOLD_BUILD_OPTS="-p ci --push=false"
  DEPLOY_HELM_ARGS=" --set backend.secrets.flagsmith\.environment\.id=m2d4r575sZ5EjkcNZ9KNaE\
   --set infra.keycloak.securityContext.runAsUser=${ns_uid}\
   --set infra.keycloak.podSecurityContext.fsGroup=${ns_uid}\
   --set infra.rabbitmq.containerSecurityContext.runAsUser=${ns_uid}\
   --set infra.rabbitmq.podSecurityContext.fsGroup=${ns_uid}"
  DEPLOY_ENV="ci"
}

profile_local(){
  echo "Profile local activated"
  SKAFFOLD_BUILD_OPTS="--skip-tests --push=false"
  DEPLOY_ENV="dev"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_user_secret_incubator="ysdPkhIdFzspxIWBcNDhXFA1kGf4LcWDg"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_keycloak_clientsecret_incubator="8kAw8iZtvg97wIa5orlQNuUjaDwjWKre"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_apicurioserver_baseurl_incubator="https://api-registry-incubator.test.paas.onemrva.priv/apis/registry/v2"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_keycloak_baseurl_incubator="https://keycloak-incubator.test.paas.onemrva.priv/"
}

init() {
  mkdir -p target
  # shellcheck disable=SC2034 # Used by xwikirc
  XWIKI_ROOT="/spaces/Scrum%20Teams/spaces/KDD/spaces/Person"
  SKAFFOLD_BUILD_OPTS=""
  MVN_VERIFY_OPTS=""
  HELM_CHART_NAME="person"
  JAVA_TOOL_OPTIONS="${JAVA_TOOL_OPTIONS} -Dfile.encoding=UTF8"
  GIT_VER_BASE_VERSION="20.0.0"
  export DEFAULT_OPENSHIFT_VERSION=v4
}

package-chart(){
  cp -r helm/ target
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/person-backend" ".backend.image"
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/person-karate-e2e" ".karate.image"
}

generate_changelog(){
  JRELEASER_PREVIOUS_TAG_NAME=$(previous_version_from_git)
  export JRELEASER_PREVIOUS_TAG_NAME
  mvn -B -N jreleaser:changelog
}

set-version(){
    simple_version_from_git
    mvn -B versions:set -D processAllModules -D newVersion=$APP_VERSION
      echo "Version"
      echo "$APP_VERSION"
}

function publish_test_artefacts {
  echo "Test Artefacts Management"
  if [ -e backend/target/jacoco.exec ]
  then
    echo "Saving test artefacts"
    curl -f -k -u ${bamboo_nexus_user}:${bamboo_nexus_secret} --upload-file "backend/target/jacoco.exec" "https://nexusprod.onemrva.priv/repository/e2e-reports/person/backend/target/jacoco.exec"
  else
    echo "Retrieving test artefacts from previous run"
    mkdir -p backend/target
    curl https://nexusprod.onemrva.priv/repository/e2e-reports/person/backend/target/jacoco.exec -o backend/target/jacoco.exec
  fi
}

sub_clean(){
  EXIT_STATUS=0
  rm -rf target || EXIT_STATUS=$?
  skaffold delete || EXIT_STATUS=$?
  mvn -B clean || EXIT_STATUS=$?
  exit $EXIT_STATUS
}

sub_help(){
    echo "Usage: $ProgName -[p|--profile <profile>] <subcommand> [options]"
    echo "Subcommands:"
    echo "    clean                      Cleans everything"
    echo "    build                      Compiles everything"
    echo "    test                       Unit test everything"
    echo "    e2e-tests                  Run e2e tests (need previous deployment in the environment)"
    echo "    next-version               Computes and set next version"
    echo "    publish                    Publish built artifacts"
    echo "    release                    Create a release of this system"
    echo "    release-bugfix             Release a bugfix (on a releases branch"
    echo "    sonarqube                  Execute Sonarqube analysis"
    echo "    sub                        Directly call a function of this script"
    echo "    deploy                     Deploy on an environment"
    echo "    undeploy                   Undeploy on an environment"
    echo ""
    echo "For help with each subcommand run:"
    echo "$ProgName <subcommand> -h|--help"
    echo ""
}

sub_next-version(){
    set-version
}

publish_api() {
  simple_version_from_git
  ls
  apicurio-cli ${DEPLOY_ENV} --operation publish --groupid be.fgov.onerva.person.backend --artifactid person-rest-api --version $APP_VERSION --file ./api/person_api.yaml
  apicurio-cli ${DEPLOY_ENV} --operation publish --groupid be.fgov.onerva.person.backend --artifactid person-async-api --version $APP_VERSION --file ./api/person_async.yaml
}

publish_microcks_report() {
  echo pushing Microcks report to rest file service...
  tar -czf microcks.tar.gz -C ./backend/target/microcks .
  curl -v -k -F "file=@microcks.tar.gz;type=application/tar+gzip" -X PUT https://rest-file-service.prod.paas.onemrva.priv/api/files/reports/person/microcks
}


sub_publish(){
  publish_test_artefacts
  publish_images_to_alpha target/metadata.yaml
  publish_helm_chart target/metadata.yaml target/helm/${HELM_CHART_NAME}
#  mvn deploy -DskipTests
  xwiki__publish_changelog "Current"
  xwiki__publish_md README ./README.md
}

sub_build(){
  simple_version_from_git
  echo "Let's build !"
  helm dependency build helm/${HELM_CHART_NAME}
  # shellcheck disable=SC2086 # We explicitly want keyword expansion
  skaffold build ${SKAFFOLD_BUILD_OPTS} --file-output=target/images.json
  create_metadata_file target/images.json target/metadata.yaml
  package-chart
  generate_changelog
  publish_microcks_report
}

sub_deploy(){
  echo "Deploying"
  if [ ${DEPLOY_ENV} != "ci" ]; then
    publish_api
  fi
  # shellcheck disable=SC2086 # We explicitly want keyword expansion
  deploy_on_openshift target/metadata.yaml ${HELM_CHART_NAME} ${DEPLOY_ENV} helm/environments/values-${DEPLOY_ENV}.yaml ${DEPLOY_HELM_ARGS}
}

sub_undeploy() {
  echo "Undeploying"
  helm --kube-context "$(tools__kubecontext_for ci)" --namespace "person-ci"  uninstall person
}

sub_e2e-tests() {
  echo "Running e2e tests"
  helm --kube-context "$(tools__kubecontext_for "${DEPLOY_ENV}")" --namespace "$(openshift_namespace "${HELM_CHART_NAME}" ${DEPLOY_ENV})" test "${HELM_CHART_NAME}" --logs --timeout 20m0s
}

sub_sonarqube(){
   mvn -B -P sonar verify jacoco:report sonar:sonar -DskipTests
}

sub_sub(){
  echo "Let's call $1"
  SUB=$1
  shift
  $SUB "$@"
}

sub_release(){
    base_release

    simple_version_branch_release
    simple_version_tag
}

sub_release-bugfix(){
  base_release
}

sub_fiddling() {
  generate_changelog
}

sub_release-beta() {
    promote_images_to_beta target/metadata.yaml
    xwiki__publish_changelog $APP_VERSION
}

base_release() {
    set-version
    promote_images_to_release target/metadata.yaml
    generate_changelog
    simple_version_from_git
    xwiki__publish_changelog "$APP_VERSION"
}

init

POSITIONAL_ARGS=()

PROFILE="local"
while [[ $# -gt 0 ]]; do
  case $1 in
    -p|--profile)
      PROFILE="$2"
      shift 2 # past argument & value
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

profile_"${PROFILE}"
if [ $? = 127 ]; then
    echo "Error: '$PROFILE' is not a known profile." >&2
    exit 1
fi


set -- "${POSITIONAL_ARGS[@]}"

subcommand=$1
case $subcommand in
    "" | "-h" | "--help")
        sub_help
        ;;
    *)
        shift
        sub_"${subcommand}" "$@"
        if [ $? = 127 ]; then
            echo "Error: '$subcommand' is not a known subcommand." >&2
            echo "       Run '$ProgName --help' for a list of known subcommands." >&2
            exit 1
        fi
        ;;
esac
