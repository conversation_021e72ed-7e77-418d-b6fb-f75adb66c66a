package be.fgov.onerva.person.backend.citizeninfo.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "commune_ds", schema = "dbo")
public class CitizenInfoComuneDs {
    @Id
    private long id;

    private int dateValid;

    private int numBox;

    private int flagValid;

    private String adresse;

    private Integer codePost;

    @Column(name = "code_resid")
    private Integer rvaCountryCode;

}
