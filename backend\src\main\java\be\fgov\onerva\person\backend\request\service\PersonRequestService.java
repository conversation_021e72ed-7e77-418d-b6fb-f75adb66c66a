package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.model.UnionDueUpdateInfo;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.wave.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@RequiredArgsConstructor
@Service
public class PersonRequestService {

    private final PersonRequestRepository repository;
    private final ApplicationEventPublisher eventPublisher;
    private final MainframeUpdateMessageFormatter messageFormatter;

    @Transactional("personTransactionManager")
    public PersonRequest createMinimalPersonInfo(String firstname, String lastname, String inss, String correlationId) {
        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .niss(inss)
                .firstname(firstname)
                .lastname(lastname)
                .correlationId(correlationId)
                .created(LocalDateTime.now())
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    @Transactional("personTransactionManager")
    public PersonRequest updatePersonInfo(CitizenUpdateRequest update, User user) {
        // Handle backward compatibility for deprecated fields
        BankUpdateInfo bankInfo = handleBankInfoBackwardCompatibility(update);
        UnionDueUpdateInfo unionDueInfo = handleUnionDueBackwardCompatibility(update);

        // Set default value dates if specific ones are not provided
        LocalDate bankInfoValueDate = bankInfo != null && bankInfo.getValueDate() != null ? bankInfo.getValueDate()
                : update.getValueDate();
        LocalDate unionDueValueDate = unionDueInfo != null && unionDueInfo.getValueDate() != null
                ? unionDueInfo.getValueDate()
                : update.getValueDate();

        // Extract payment type from bank info if available
        PaymentType paymentType = bankInfo != null ? bankInfo.getPaymentType() : update.getPaymentType();

        // Extract union due from union due info if available
        Boolean unionDue = unionDueInfo != null ? unionDueInfo.getUnionDue() : update.getUnionDue();

        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.UPDATE)
                .niss(update.getNiss())
                .nationalityCode(update.getNationalityCode())
                .paymentType(paymentType)
                .unionDue(unionDue)
                .valueDate(update.getValueDate())
                .address(update.getAddress())
                .username(user.getUsername())
                .operatorCode(getOperatorCode(user))
                .correlationId(update.getCorrelationId())
                .created(LocalDateTime.now())
                // Add new fields from CitizenUpdateRequest
                .birthDate(update.getBirthDate() != null ? LocalDate.parse(update.getBirthDate(),
                        DateTimeFormatter.ofPattern("yyyyMMdd")) : null)
                .languageCode(update.getLanguageCode())
                // Add bank info fields
                .iban(bankInfo != null ? bankInfo.getIban() : null)
                .bic(bankInfo != null ? bankInfo.getBic() : null)
                .accountHolder(bankInfo != null ? bankInfo.getAccountHolder() : null)
                .bankInfoValueDate(bankInfoValueDate)
                .unionDueValueDate(unionDueValueDate)
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    /**
     * Handles backward compatibility for deprecated paymentType field.
     * If bankInfo is null but paymentType is provided, creates a new
     * BankUpdateInfo.
     *
     * @param update the citizen update request
     * @return the bank update info, possibly created from deprecated fields
     */
    private BankUpdateInfo handleBankInfoBackwardCompatibility(CitizenUpdateRequest update) {
        if (update.getBankInfo() != null) {
            return update.getBankInfo();
        }

        if (update.getPaymentType() != null) {
            return BankUpdateInfo.builder()
                    .paymentType(update.getPaymentType())
                    .valueDate(update.getValueDate())
                    .build();
        }

        return null;
    }

    /**
     * Handles backward compatibility for deprecated unionDue field.
     * If unionDueInfo is null but unionDue is provided, creates a new
     * UnionDueUpdateInfo.
     *
     * @param update the citizen update request
     * @return the union due update info, possibly created from deprecated fields
     */
    private UnionDueUpdateInfo handleUnionDueBackwardCompatibility(CitizenUpdateRequest update) {
        if (update.getUnionDueInfo() != null) {
            return update.getUnionDueInfo();
        }

        if (update.getUnionDue() != null) {
            return UnionDueUpdateInfo.builder()
                    .unionDue(update.getUnionDue())
                    .valueDate(update.getValueDate())
                    .build();
        }

        return null;
    }

    Integer getOperatorCode(User user) {
        if (!user.getOperatorCodes().isEmpty()) {
            try {
                return Integer.parseInt(user.getOperatorCodes().getFirst());
            } catch (NumberFormatException e) {
                log.warn("Could not convert operator code to an int! ... {}", user);
            }
        }
        return null;
    }
}
