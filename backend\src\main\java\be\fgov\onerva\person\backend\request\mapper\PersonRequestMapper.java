package be.fgov.onerva.person.backend.request.mapper;

import backend.rest.model.CitizenRequestDTO;
import backend.rest.model.CitizenRequestPageDTO;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;

@Mapper
public interface PersonRequestMapper {

    CitizenRequestDTO toDto(PersonRequest entity);

    @Mapping(source = "number", target = "pageNumber")
    @Mapping(source = "size", target = "pageSize")
    @Mapping(source = "totalPages", target = "totalPage")
    @Mapping(source = "first", target = "isFirst")
    @Mapping(source = "last", target = "isLast")
    CitizenRequestPageDTO toDto(Page<PersonRequest> page);

    static OffsetDateTime map(LocalDateTime value) {
        if (value == null) return null;
        return value.atZone(ZoneId.of("Europe/Brussels")).toOffsetDateTime();
    }
}
