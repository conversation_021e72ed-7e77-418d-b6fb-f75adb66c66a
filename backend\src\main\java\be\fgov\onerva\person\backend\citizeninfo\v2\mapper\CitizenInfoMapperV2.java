package be.fgov.onerva.person.backend.citizeninfo.v2.mapper;

import backend.rest.model.CitizenInfoPageV2DTO;
import backend.rest.model.CitizenInfoV2DTO;
import backend.rest.model.FlagDTO;
import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.data.domain.Page;

@Mapper(componentModel = "spring")
public interface CitizenInfoMapperV2 {
    @Mapping(target = "numPens", source = "numPens")
    @Mapping(target = "numBox", source = "numBox")
    @Mapping(target = "flagPurge", source = "flagPurge", qualifiedByName = "convertFlag")
    @Mapping(target = "ssin", ignore = true)
    @Mapping(target = "firstName", ignore = true)
    @Mapping(target = "lastName", ignore = true)
    @Mapping(target = "address", ignore = true)
    @Mapping(target = "postalCode", ignore = true)
    @Mapping(target = "rvaCountryCode", ignore = true)
    @Mapping(target = "OP", ignore = true)
    @Mapping(target = "unemploymentOffice", ignore = true)
    @Mapping(target = "deceasedDate", ignore = true)
    @Mapping(target = "iban", ignore = true)
    @Mapping(target = "language", ignore = true)
    @Mapping(target = "sex", ignore = true)
    @Mapping(target = "bisNumber", ignore = true)
    @Mapping(target = "flagNation", ignore = true)
    @Mapping(target = "telephoneOnem", ignore = true)
    @Mapping(target = "gsmOnem", ignore = true)
    @Mapping(target = "telephoneReg", ignore = true)
    @Mapping(target = "gsmReg", ignore = true)
    @Mapping(target = "email", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "flagVCpte", ignore = true)
    @Mapping(target = "nationBcss", ignore = true)
    @Mapping(target = "communeDateValid", ignore = true)
    @Mapping(target = "dateMcpte", ignore = true)
    @Mapping(target = "nationDateValid", ignore = true)
    CitizenInfoV2DTO map(CitizenInfoEntity entity);

    @Named("convertFlag")
    static String convertFlag(boolean flag) {
        return flag ? FlagDTO.n.name() : FlagDTO.y.name();
    }
    
    @Mapping(source = "number", target = "pageNumber")
    @Mapping(source = "size", target = "pageSize")
    @Mapping(source = "totalPages", target = "totalPage")
    @Mapping(source = "totalElements", target = "totalElements")
    @Mapping(source = "first", target = "isFirst")
    @Mapping(source = "last", target = "isLast")
    CitizenInfoPageV2DTO mapPageToDto(Page<CitizenInfoEntity> source);

}
