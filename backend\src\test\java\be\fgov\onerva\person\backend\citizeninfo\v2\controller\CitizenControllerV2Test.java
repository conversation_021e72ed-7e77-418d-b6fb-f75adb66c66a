package be.fgov.onerva.person.backend.citizeninfo.v2.controller;

import backend.rest.model.CitizenInfoPageV2DTO;
import backend.rest.model.CitizenInfoV2DTO;
import backend.rest.model.FlagDTO;
import be.fgov.onerva.person.backend.IntegrationTest;
import be.fgov.onerva.person.backend.citizeninfo.v2.service.CitizenInfoServiceV2;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

//@Sql(value = "/scripts/init-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
//@Sql(value = "/scripts/delete-data.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class CitizenControllerV2Test extends IntegrationTest {

    private static final String BASE_PATH_V2 = "/api/v2/citizen";

    @Mock
    private CitizenInfoServiceV2 citizenInfoServiceV2;

    //TODO Refactor this test to mock return values
    @Disabled
    @Test
    void getInfoByNissV2() {

        var citizenInfoPageV2DTO = new CitizenInfoPageV2DTO();
        var citizenInfoDTO = new CitizenInfoV2DTO();
        citizenInfoDTO.setNumPens(new BigDecimal(998624049));
        citizenInfoDTO.setLastName("LASTNAME5");
        citizenInfoDTO.setFirstName("NAME5");
        citizenInfoDTO.setSsin("99062404991");
        citizenInfoDTO.setAddress("ADDRESS4 1073");
        citizenInfoDTO.setPostalCode("1082");
        citizenInfoDTO.setNumBox(new BigDecimal(38448));
        citizenInfoDTO.setOP(BigDecimal.valueOf(3210));
        citizenInfoDTO.setIban("");
        citizenInfoDTO.setUnemploymentOffice(BigDecimal.valueOf(21));
        citizenInfoDTO.setFlagPurge(FlagDTO.n.name());
        citizenInfoDTO.setFlagNation(BigDecimal.valueOf(150));
        citizenInfoDTO.setDeceasedDate(null);
        citizenInfoPageV2DTO.setContent(List.of(citizenInfoDTO));
        citizenInfoPageV2DTO.setTotalPage(1);
        citizenInfoPageV2DTO.setTotalElements(1);
        citizenInfoPageV2DTO.setPageNumber(0);
        citizenInfoPageV2DTO.setPageSize(10);

        citizenInfoPageV2DTO.setContent(List.of(citizenInfoDTO));

        when(citizenInfoServiceV2.getCitizenInfoBySsinListV2(any(),any()))
                .thenReturn(citizenInfoPageV2DTO);

        var page = new CitizenInfoPageV2DTO()
                .content(Collections.singletonList(citizenInfoDTO))
                .totalPage(1)
                .totalElements(1)
                .pageNumber(0)
                .pageSize(10)
                .isFirst(true)
                .isLast(true);

        rest.get()
                .uri(BASE_PATH_V2 + "/info?ssins=99062404991&pageNumber=0&pageSize=10")
                .exchange()
                .assertThat()
                .hasStatusOk();
//                .expectBody(CitizenInfoPageV2DTO.class)
//                .isEqualTo(page);
    }

}