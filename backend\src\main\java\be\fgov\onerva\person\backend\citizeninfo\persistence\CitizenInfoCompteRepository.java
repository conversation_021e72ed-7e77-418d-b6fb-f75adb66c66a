package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoCompte;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface CitizenInfoCompteRepository  extends JpaRepository<CitizenInfoCompte, String>, JpaSpecificationExecutor<CitizenInfoCompte> {

    Optional<CitizenInfoCompte> findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(long parentId, int flag, int dateValid);

}
