package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoComuneDs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CitizenInfoComuneDsRepository extends JpaRepository<CitizenInfoComuneDs, String>, JpaSpecificationExecutor<CitizenInfoComuneDs> {

    List<CitizenInfoComuneDs> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox, int flagValid, int dateValid);
}
