package be.fgov.onerva.person.backend.citizeninfo.service;

import backend.rest.model.*;
import be.fgov.onerva.common.utils.InssUtils;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizeninfo.mapper.AddressUtils;
import be.fgov.onerva.person.backend.citizeninfo.mapper.DateUtils;
import be.fgov.onerva.person.backend.citizeninfo.model.*;
import be.fgov.onerva.person.backend.citizeninfo.persistence.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class CitizenInfoService {

    private static final BigDecimal BELGIAN_NATIONALITY = BigDecimal.valueOf(150);
    private static final int BELGIUM = 1;
    private static final int LATEST = 9;

    private final CitizenInfoRepository infoRepository;
    private final CitizenInfoCompteRepository compteRepository;
    private final CitizenInfoGeneralRepository generalRepository;
    private final CitizenInfoNationRepository nationRepository;
    private final CitizenInfoCommunicRepository communicRepository;
    private final CitizenInfoComuneDsRepository comuneDsRepository;
    private final CitizenInfoCommuneEtrDsRepository communeEtrDsRepository;
    private final CitizenInfoSiggenDsRepository siggenDsRepository;
    private final CitizenInfoSectopDsRepository sectopDsRepository;
    private final CitizenInfoProfDsRepository profDsRepository;
    private final CitizenInfoContratDsRepository contratDsRepository;

    @Transactional(readOnly = true)
    public CitizenInfoPageDTO findCitizenInfo(List<String> ssin, List<Integer> numbox, Pageable pageable) {
        boolean ssinEmpty = CollectionUtils.isEmpty(ssin);
        boolean numboxEmpty = CollectionUtils.isEmpty(numbox);

        if (ssinEmpty && numboxEmpty) {
            return toPage(List.of(), pageable);
        } else if (!ssinEmpty && !numboxEmpty) {
            throw new IllegalArgumentException("Choose parameter ssins or citizedId but not both.");
        }

        List<CitizenInfoEntity> citizensWithSsinHistory;
        if (!ssinEmpty) {
            List<String> invalidSsins = ssin.stream().filter(this::inssInvalid).toList();
            if (invalidSsins.size() > 0) {
                throw new IllegalArgumentException("Given ssins are invalid " + invalidSsins);
            }

            citizensWithSsinHistory = findCitizensWithSsinHistory(ssin);
        } else {
            citizensWithSsinHistory = infoRepository.findByNumBoxIn(numbox);
        }

        var citizens = citizensWithSsinHistory.stream().filter(c -> c.getLastId() == LATEST).toList();
        if (citizens.isEmpty()) {
            return toPage(List.of(), pageable);
        }

        int toDay = DateUtils.currentDate();
        var citizenIds = citizens.stream().map(CitizenInfoEntity::getNumBox).toList();
        var ssinHistoryById = getSsinHistoryById(citizensWithSsinHistory);

        var infoByCitizenId = generalRepository.getCitizenInfoGeneralByNumBoxIn(citizenIds).stream().collect(Collectors.toMap(CitizenInfoGeneral::getNumBox, Function.identity()));
        var nationalities = nationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);
        var contactsInfo = communicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);
        var contracts = contratDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds,LATEST, toDay);
        var addresses = comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);
        var sectops = sectopDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);
        var profs = profDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);

        var dtos = citizens.stream()
                .filter(citizen -> infoByCitizenId.containsKey(citizen.getNumBox()))
                .map(citizen -> {
            int citizenId = citizen.getNumBox();

            CitizenInfoDTO dto = new CitizenInfoDTO();
            dto.setNumBox(BigDecimal.valueOf(citizenId));
            dto.setSsin(toSsin(citizen.getNumPens()));
            dto.setNumPens(BigDecimal.valueOf(citizen.getNumPens()));
            dto.setFlagToPurge(citizen.isFlagToPurge() ? FlagDTO.y.name() : FlagDTO.n.name());

            var generalInfo = infoByCitizenId.get(citizenId);
            mapGeneralInfo(dto, generalInfo);
            setPersonalInfo(dto, generalInfo);
            setPaymentInstitutionInfo(dto, sectops);
            setAddress(dto, addresses);
            setNationalityCode(dto, generalInfo, nationalities);
            setBankAccount(dto, generalInfo);
            setContactInformation(dto, contactsInfo);
            setEmploymentContract(dto, contracts);
            dto.setBisNumber(ssinHistoryById.get(citizenId));

            dto.setLastModifDate(computeGreatestDateValid(generalInfo, addresses, sectops, profs, contracts, nationalities));

            return dto;
        }).toList();

        return toPage(dtos, pageable);
    }

    List<CitizenInfoEntity> findCitizensWithSsinHistory(List<String> ssin) {
        List<Integer> numpens = ssin.stream().map(el -> PensionNumberUtils.convertFromInss(Long.valueOf(el))).toList();
        return infoRepository.findByNumPensIn(numpens);
    }

    Map<Integer,List<String>> getSsinHistoryById(List<CitizenInfoEntity> citizenHistory) {
        return citizenHistory.stream()
                .filter(info -> info.getLastId() != LATEST)
                .collect(Collectors.groupingBy(
                        CitizenInfoEntity::getNumBox,
                        Collectors.mapping(info -> toSsin(info.getNumPens()), Collectors.toList())
                ));
    }

    void mapGeneralInfo(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
        if (generalInfo.getFullName() != null) {
            var name = trim(generalInfo.getFullName());
            if (name.contains(",")) {
                var parts = name.split(",");
                dto.setLastName(parts[0].trim());
                dto.setFirstName(parts[1].trim());
            } else {
                dto.setLastName(name);
            }
        }

        dto.setSex(SexDTO.fromValue(generalInfo.getSex()).name());
        dto.setBirthDate(DateUtils.convertToDate(generalInfo.getBirthDate()));
        dto.setDeceasedDate(DateUtils.convertToDate(generalInfo.getDeceasedDate()));
        dto.setUnemploymentOffice(toBigDecimal(generalInfo.getUnemploymentOffice()));
    }

    void setPaymentInstitutionInfo(CitizenInfoDTO dto, List<CitizenInfoSectopDs> sectops) {
        var piHistory = sectops.stream()
                .filter(piInfo -> piInfo.getNumBox() == dto.getNumBox().intValue())
                .toList();

        piHistory.stream()
                .findFirst()
                .ifPresent(piInfo ->
                    dto.setOP(toBigDecimal(piInfo.getSectOp()))
                );

        boolean hasUnionDueOnce = piHistory.stream().anyMatch(CitizenInfoSectopDs::hasUnionDue);
        if (hasUnionDueOnce) {// only give info about mandate if there was one once
            CitizenInfoSectopDs piFound = null;
            for (int i = 0; i < piHistory.size(); i++) {
                var current = piHistory.get(i);
                if (piHistory.size() == 1) {
                    piFound = current;
                    break;
                }
                var previous = piHistory.get(i + 1);
                if (current.hasUnionDue() != previous.hasUnionDue()) {
                    piFound = current;
                    break;
                } else if (i + 2 == piHistory.size()) {
                    piFound = previous;
                    break;
                }
            }
            dto.setUnionDue(new CitizenInfoUnionDueDTO(piFound.hasUnionDue(), DateUtils.convertToDate(piFound.getDateValid())));
        }
    }

    void setAddress(CitizenInfoDTO dto, List<CitizenInfoComuneDs> adresses) {
        int citizenId = dto.getNumBox().intValue();
        adresses.stream()
                .filter(address -> address.getNumBox() == citizenId)
                .findFirst()
                .ifPresent(address -> {
                    String addressLine = trim(address.getAdresse());
                    dto.setAddress(addressLine);

                    if (address.getRvaCountryCode() != null) {
                        // normally the code-resid is the NIS code of a belgian town but if smaller than a normal NIS code it is the NEO country code
                        if (address.getRvaCountryCode() > 9999) {
                            String zip = address.getCodePost() == null ? null : address.getCodePost().toString();
                            dto.setPostalCode(zip);
                            var streetParts = AddressUtils.splitStreet(addressLine);
                            var numberParts = AddressUtils.splitHouseNumberAndBox(streetParts[1]);
                            dto.setAddressObj(new ForeignAddressDTO()
                                    .street(streetParts[0])
                                    .number(numberParts[0])
                                    .box(numberParts[1])
                                    .zip(zip)
                                    .countryCode(BELGIUM)
                                    .validFrom(DateUtils.convertToDate(address.getDateValid()))
                            );
                        } else {
                            setForeignAddress(dto, address.getRvaCountryCode(), citizenId);
                        }
                    }
                });
    }

    void setForeignAddress(CitizenInfoDTO dto, int countryCode, int citizenId) {
        communeEtrDsRepository.findFirstByNumBoxAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenId, LATEST, DateUtils.currentDate())
                .ifPresent(adr -> {
                    String zip = trim(adr.getZip());
                    dto.setPostalCode(zip);
                    var numberParts = AddressUtils.splitHouseNumberAndBox(trim(adr.getNumber()));
                    dto.setAddressObj(new ForeignAddressDTO()
                            .street(trim(adr.getStreet()))
                            .number(numberParts[0])
                            .box(numberParts[1])
                            .zip(zip)
                            .city(trim(adr.getCity()))
                            .countryCode(countryCode)
                            .validFrom(DateUtils.convertToDate(adr.getDateValid()))
                    );
                });
    }

    void setContactInformation(CitizenInfoDTO dto, List<CitizenInfoCommunic> contactsInfo) {
        contactsInfo.stream()
                .filter(contactInfo -> contactInfo.getNumBox() == dto.getNumBox().intValue())
                .findFirst()
                .ifPresent(contactInfo -> {
                        dto.setEmail(trim(contactInfo.getEmail()));
                        dto.setTelephoneOnem(trim(contactInfo.getTelephoneOnem()));
                        dto.setTelephoneReg(trim(contactInfo.getTelephoneReg()));
                        dto.setGsmOnem(trim(contactInfo.getGsmOnem()));
                        dto.setGsmReg(trim(contactInfo.getGsmReg()));
                        dto.setEmailReg(trim(contactInfo.getEmailReg()));
                });
    }

    void setEmploymentContract(CitizenInfoDTO dto, List<CitizenInfoContratDs> contracts) {
        contracts.stream()
                .filter(contactInfo -> contactInfo.getNumBox() == dto.getNumBox().intValue())
                .findFirst()
                .map(CitizenInfoContratDs::getEmploymentContract)
                .ifPresent(dto::setEmploymentContract);
    }

    void setNationalityCode(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo, List<CitizenInfoNation> nationalities) {
        if (generalInfo.isFlagNation()) {
            dto.setFlagNation(
                    nationalities.stream().filter(nationality -> nationality.getNumBox() == dto.getNumBox().intValue() && nationality.getNation() != null)
                            .map(nationality -> BigDecimal.valueOf(nationality.getNation()))
                            .findFirst()
                            .orElse(BELGIAN_NATIONALITY)
            );
        } else {
            dto.setFlagNation(BELGIAN_NATIONALITY);
        }
    }

    void setBankAccount(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
        int toDay = DateUtils.currentDate();
        if (generalInfo.isFlagVCpte() && generalInfo.getDateVCpte() <= toDay) {
            fillBankAccount(dto, trim(generalInfo.getIban()), trim(generalInfo.getBic()), trim(generalInfo.getBankAccountHolder()), generalInfo.getDateVCpte());
        } else {
            compteRepository.findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(generalInfo.getId(), LATEST, toDay)
                    .ifPresent(bankAccount ->
                            fillBankAccount(dto, trim(bankAccount.getIban()), trim(bankAccount.getBic()), trim(bankAccount.getBankAccountHolder()), bankAccount.getDateValid())
                    );
        }
    }

    void fillBankAccount(CitizenInfoDTO dto, String iban, String bic, String holder, int validFrom) {
        if (iban != null) {
            dto.setIban(iban);
            dto.setBankAccount(new BankAccountDTO()
                    .iban(iban)
                    .bic(bic)
                    .holder(holder)
                    .validFrom(DateUtils.convertToDate(validFrom))
            );
        }
    }

    void setPersonalInfo(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
        int toDay = DateUtils.currentDate();
        if (generalInfo.isFlagSiggen() && generalInfo.getDateVSiggen() <= toDay) {
            setLanguageAndPaymentMode(dto, generalInfo.getLanguage(), generalInfo.getPaymentMode());
        } else {
            siggenDsRepository.findTopByParentIdAndFlagValidOrderByDateValidDesc(generalInfo.getId(), LATEST)
                    .ifPresent(siggen ->
                            setLanguageAndPaymentMode(dto, siggen.getLanguage(), siggen.getPaymentMode())
                    );
        }
    }

    void setLanguageAndPaymentMode(CitizenInfoDTO dto, Integer language, Integer paymentMode) {
        if (language != null) {
            dto.setLanguage(LanguageDTO.fromValue(language).name());
        }
        dto.setPaymentMode(paymentMode);
    }

    /**
     To be removed if not needed.
     From KDD analysis:
        The validity date in the upper right corner is computed based on different tables.
        It is the last date among the 6 following dates (attention: it is possible that one of those dates is null):
        date_v1: [date_v_siggen] if [flag_v_siggen] = 1 in table [general_ds] otherwise last [date_valid] in table [siggen_ds] for given [num_box] and [flag_valid] = 9
        date_v2: last [date_valid] in table [commune_ds] for given [num_box] and [flag_valid] = 9
        date_v3: last [date_valid] in table [sectop_ds] for given [num_box] and [flag_valid] = 9
        date_v4: last [date_valid] in table [prof_ds] for given [num_box] and [flag_valid] = 9
        date_v5: last [date_valid] in table [contrat_ds] for given [num_box] and [flag_valid] = 9
        date_v6: last [date_valid] in table [nation_ds] for given [num_box] and [flag_valid] = 9
     */
    Integer computeGreatestDateValid(
            CitizenInfoGeneral generalInfo,
            List<CitizenInfoComuneDs> addresses,
            List<CitizenInfoSectopDs> sectops,
            List<CitizenInfoProfDs> profs,
            List<CitizenInfoContratDs> contracts,
            List<CitizenInfoNation> nationalities
    ) {
        var dateList = new ArrayList<Integer>();
        int citizenId = generalInfo.getNumBox();

        if (generalInfo.isFlagSiggen()) {
            dateList.add(generalInfo.getDateVSiggen());
        } else {
            siggenDsRepository.findTopByParentIdAndFlagValidOrderByDateValidDesc(generalInfo.getId(), LATEST)
                    .map(CitizenInfoSiggenDs::getDateValid)
                    .ifPresent(dateList::add);
        }

        addresses.stream()
                .filter(address -> address.getNumBox() == citizenId)
                .findFirst()
                .map(CitizenInfoComuneDs::getDateValid)
                .ifPresent(dateList::add);

        sectops.stream()
                .filter(sectop -> sectop.getNumBox() == citizenId)
                .findFirst()
                .map(CitizenInfoSectopDs::getDateValid)
                .ifPresent(dateList::add);

        profs.stream()
                .filter(prof -> prof.getNumBox() == citizenId)
                .findFirst()
                .map(CitizenInfoProfDs::getDateValid)
                .ifPresent(dateList::add);

        contracts.stream()
                .filter(contract -> contract.getNumBox() == citizenId)
                .findFirst()
                .map(CitizenInfoContratDs::getDateValid)
                .ifPresent(dateList::add);

        nationalities.stream()
                .filter(nationality -> nationality.getNumBox() == citizenId)
                .findFirst()
                .map(CitizenInfoNation::getDateValid)
                .ifPresent(dateList::add);

        return dateList.isEmpty() ? null : Collections.max(dateList);
    }

    boolean inssInvalid(String inss) {
        try {
            return !InssUtils.isValid(Long.parseLong(inss));
        } catch (NumberFormatException e) {
            return true;
        }
    }

    String toSsin(int numPens) {
        return String.format("%011d", PensionNumberUtils.convertToInssWithDefault(numPens));
    }

    CitizenInfoPageDTO toPage(List<CitizenInfoDTO> dtos, Pageable pageable) {
        return new CitizenInfoPageDTO()
                .pageSize(pageable.getPageSize())
                .pageNumber(pageable.getPageNumber())
                .content(dtos)
                .isFirst(true)
                .isLast(true)
                .totalElements(dtos.size())
                .totalPage(dtos.isEmpty() ? 0 : 1);
    }

    static String trim(String s) {
        if (s == null) {
            return null;
        }
        String trimmed = s.trim();
        return trimmed.isEmpty() ? null : trimmed;
    }

    BigDecimal toBigDecimal(Number n) {
        return n == null? null : BigDecimal.valueOf(n.longValue());
    }
}
