package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.*;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "prof_ds", schema = "dbo")
public class CitizenInfoProfDs {

    @Id
    private long id;

    private int numBox;

    private int flagValid;

    private int dateValid;
}
