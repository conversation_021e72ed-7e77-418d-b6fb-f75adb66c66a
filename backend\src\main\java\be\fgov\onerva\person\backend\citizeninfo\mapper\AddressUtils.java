package be.fgov.onerva.person.backend.citizeninfo.mapper;

import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@UtilityClass
public final class AddressUtils {

    private static final Pattern NBR_BOX = Pattern.compile("^(\\d+\\s*[A-Za-z]?)\\s*(/|\\*|x|bte|bus|bt|b)\\s*(\\S+)$", Pattern.CASE_INSENSITIVE);

    private static final List<String> BOX_SEPARATORS = List.of("bte", "bus", "bt", "b");
    private static final List<String> SPECIALS = List.of("app", "ap");


    public static String[] splitStreet(String line) {
        int start = indexOfFirstChar(line, 0, false);
        int index = indexOfFirstChar(line, start, true);
        if (index <= 0) {
            return new String[]{line, null};
        }

        String street = line.substring(0, index - 1).trim();
        int endIndex = street.charAt(street.length() - 1) == ',' ? street.length() - 1 : street.length();
        return new String[]{street.substring(0, endIndex), line.substring(index).trim()};
    }

    public static String[] splitHouseNumberAndBox(String line) {
        if (line == null) {
            return new String[]{null, null};
        }
        var trimmed = line.trim();
        boolean hasSpecials = SPECIALS.stream().anyMatch(s -> trimmed.toLowerCase().contains(s));

        Matcher matcher = NBR_BOX.matcher(trimmed);
        if (!matcher.matches() || hasSpecials) {
            return new String[]{trimmed, null};
        }

        String separator = matcher.group(2);
        String box = matcher.group(3);

        if (separator == null ||  box == null) {
            return new String[]{trimmed, null};
        }

        String nbr = matcher.group(1).trim();
        separator = separator.trim();
        box = box.trim();

        if ("b".equalsIgnoreCase(separator) && !Character.isDigit(box.charAt(0))) {
            return new String[]{trimmed, null};
        }

        if ("/".equals(separator) && box.length() > 1) {
            for(String sep: BOX_SEPARATORS) {
                int index = box.toLowerCase().indexOf(sep);
                if (index != -1) {
                    return new String[]{nbr, box.substring(index + sep.length()).trim()};
                }
            }
        }

        return new String[]{nbr, box};
    }

    static int indexOfFirstChar(String line, int index, boolean digit) {
        if (line == null || index < 0 || index >= line.length()) {
            return -1;
        }
        for (int i = index; i < line.length(); i++) {
            boolean isDigit = Character.isDigit(line.charAt(i));
            if (digit? isDigit : !isDigit) {
                return i;
            }
        }
        return -1;
    }
}
