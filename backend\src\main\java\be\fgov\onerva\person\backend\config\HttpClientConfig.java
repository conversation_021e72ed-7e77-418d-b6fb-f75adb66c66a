package be.fgov.onerva.person.backend.config;

import be.fgov.onerva.wave.ApiClient;
import be.fgov.onerva.wave.api.UserApi;
import be.fgov.onerva.wave.model.User;
import be.fgov.onerva.wave.model.UserCriteria;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.client.OAuth2ClientHttpRequestInterceptor;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClientResponseException;

@Configuration
public class HttpClientConfig {

    public static final String CLIENT_REGISTRATION_ID = "keycloak";
    public static final String DEV_PROFILE = "(dev | ci | local | default)";

    @Bean @Profile("!" + DEV_PROFILE)
    UserApi userApi(RestClient.Builder builder, @Value("${wave.api}") String waveUrl) {
        ApiClient apiClient = new ApiClient(builder.build());
        apiClient.setBasePath(waveUrl);

        return new UserApi(apiClient);
    }

    @Bean @Profile("!" + DEV_PROFILE)
    OAuth2AuthorizedClientManager authorizedClientManager(
            OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
            ClientRegistrationRepository clientRegistrationRepository
    ) {
        return new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, oAuth2AuthorizedClientService);
    }

    @Bean @Profile({"!" + DEV_PROFILE})
    RestClientCustomizer globalOAuthClientInterceptor(OAuth2AuthorizedClientManager authorizedClientManager) {
        var interceptor = new OAuth2ClientHttpRequestInterceptor(authorizedClientManager);
        interceptor.setClientRegistrationIdResolver(req -> CLIENT_REGISTRATION_ID);

        return  builder -> builder.requestInterceptor(interceptor);
    }

    @Bean @Profile(DEV_PROFILE)
    UserApi mockUserApi() {
        return new UserApi() {
            @Override
            public User searchUsers(UserCriteria criteria) throws RestClientResponseException {
                return new User()
                        .firstname("James")
                        .lastname("Bond")
                        .addOperatorCodesItem("007")
                        .username(criteria.getUsername() != null? criteria.getUsername() : "jbond")
                        .inss(criteria.getInss() != null? criteria.getInss() : "77070700791");
            }
        };
    }

}
