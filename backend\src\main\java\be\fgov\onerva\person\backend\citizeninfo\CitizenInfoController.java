package be.fgov.onerva.person.backend.citizeninfo;

import backend.api.CitizenInfoApi;
import backend.rest.model.CitizenInfoPageDTO;
import be.fgov.onerva.person.backend.citizeninfo.service.CitizenInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.springframework.http.ResponseEntity.ok;

@RequiredArgsConstructor
@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api")
public class CitizenInfoController implements CitizenInfoApi {

    private final CitizenInfoService service;


    @Override public ResponseEntity<CitizenInfoPageDTO> searchCitizenInfo(List<String> ssins, List<Integer> citizenId, String dataReturned, Integer pageNumber, Integer pageSize) {
        var citizenInfo = service.findCitizenInfo(ssins, citizenId, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, "numPens")));
        return ok(citizenInfo);
    }

}
