package be.fgov.onerva.person.backend.request.mapper;

import org.mapstruct.Mapping;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.CLASS)

@Mapping(target = "id", expression = "java(java.util.UUID.randomUUID().toString())")
@Mapping(target = "time", expression = "java(java.time.LocalDateTime.now().toString())")
@Mapping(target = "subject", ignore = true)
@Mapping(target = "specversion", constant = "1.0")
@Mapping(target = "source", constant = "RVAONEM_PERSON_API")
@Mapping(target = "dataschema", ignore = true)
@Mapping(target = "datacontenttype", constant = "application/json")
@Mapping(target = "additionalProperties", ignore = true)
@interface CloudEventMapping {
}
