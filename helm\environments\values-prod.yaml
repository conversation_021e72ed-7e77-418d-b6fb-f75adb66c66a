global:
  environment: prod
  registry: docker-release.onemrva.priv
  routes:
    host: person.prod.paas.onemrva.priv

backend:
  springConfiguration:
    spring:
      security:
        oauth2:
          resource-server:
            jwt:
              issuer-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents
          client:
            provider:
              keycloak:
                issuer-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local

    wave:
      api: https://wo-configurator.prod.paas.onemrva.priv/api

    datasource:
      mfx:
        url: *****************************************************************************************************************************************************************;
        username: CORSEA_Person
      person:
        url: *****************************************************************************************************************************************************
        username: person_java_MSSQL

    ibm:
      mq:
        connName: crossprod.onemrva.priv(1608)
        user: MQJAVAPROD@onemrva
        queueManager: QM_WEB_PROD
        channel: CHSRVC_WEB_UBENEFIT
        useAuthenticationMQCSP: false

    rabbitmq:
      oauth:
        enabled: true
        clientId: person-backend
        tokenEndpointUri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token

kcconfig:
  keycloak:
    url: https://keycloak.prod.paas.onemrva.priv/
