package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoSiggenDs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


public interface CitizenInfoSiggenDsRepository extends JpaRepository<CitizenInfoSiggenDs, String>, JpaSpecificationExecutor<CitizenInfoSiggenDs> {

    Optional<CitizenInfoSiggenDs> findTopByParentIdAndFlagValidOrderByDateValidDesc(long parentId, int flagValid);
}
