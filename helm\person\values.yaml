
backend:
  enabled: true
  image:
    repository: "onemrva/person-backend"
  route:
    paths:
      - /api
      - /actuator
      - /swagger-ui
      - /v3
  proxy:
    enabled: false
  secrets:
    "datasource.mfx.password": "D3vD3vD3v$"
    "datasource.person.password": "p3rs0n-pwd"
    "ibm.mq.password": ""
    "rabbitmq.password": ""
    "flagsmith.environment.id": "QFA37efDPrx4J6YsP5mHJc"
    "application.nationalcitizendefault": "150"
  extraEnv:
    - name: SPRING_PROFILES_ACTIVE
      value: "{{ .Values.global.environment }}"
    - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: rabbitmq.password
    - name: DATASOURCE_MFX_PASSWORD
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: datasource.mfx.password
    - name: DATASOURCE_PERSON_PASSWORD
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: datasource.person.password
    - name: FLAGSMITH_ENVIRONMENT_ID
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: flagsmith.environment.id
    - name: RABBITMQ_OAUTH_CLIENTSECRET
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: rabbitmq.password
    - name: IBM_MQ_PASSWORD
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: ibm.mq.password
    - name: APPLICATION_NATIONAL_CITIZEN_DEFAULT
      valueFrom:
        secretKeyRef:
          name: person-backend
          key: application.nationalcitizendefault

kcconfig:
  image:
    registry: "docker-release.onemrva.priv"
  enabled: true
  jobAnnotations:
    "helm.sh/hook": post-install,post-upgrade,post-rollback
    "helm.sh/hook-delete-policy": before-hook-creation
    "helm.sh/hook-weight": "10"
  keycloak:
    url: http://keycloak-http
    user: admin
    password: admin
  realm:
    name: 'onemrva-agents'
    clients:
      person-backend:
        description: Person backend API
        secret: person-secret
        standardFlowEnabled: false
        directAccessGrantsEnabled: true
        serviceAccountsEnabled: true
        protocolMappers:
          - name: aud
            protocol: openid-connect
            protocolMapper: oidc-audience-mapper
            consentRequired: "false"
            config:
              included.custom.audience: rabbitmq
              id.token.claim: "true"
              access.token.claim: "true"
          - name: "Rabbit Permissions"
            protocol: openid-connect
            protocolMapper: oidc-hardcoded-claim-mapper
            consentRequired: "false"
            config:
              claim.name: extra_scope
              claim.value: "rabbitmq.configure:onemrva/person.exchange rabbitmq.write:onemrva/person.exchange"
              jsonType.label: String
              id.token.claim: "false"
              access.token.claim: "true"

infra:
  enabled: false

  mssql:
    enabled: true
    sapassword: D3vD3vD3v$
    acceptEula:
      value: "y"
    init:
      configmap:
        nametemplate: 'person-mssql-linux-init'

  keycloak:
    enabled: true
    fullnameOverride: keycloak

  rabbitmq:
    enabled: true

db:
  sapassword: p3rs0n-pwd
  service:
    port: 1444
  acceptEula:
    value: "y"
  edition:
    value: Express
  persistence:
    enabled: false
  init:
    configmap:
      nametemplate: ''
  resources:
    limits:
      cpu: 500m
      memory: 3036Mi
    requests:
      cpu: 500m
      memory: 3036Mi

karate:
  enabled: false
  env:
  image:
    registry: docker-alpha.onemrva.priv
    repository: onemrva/person-karate-e2e
    tag: latest
