project:
  name: person
  description: person
  longDescription: |
    person
  website: https://bitbucket/projects/CORSEA/repos/person/browse
  docsUrl: https://bitbucket/projects/CORSEA/repos/person/browse
  license: ONEmRVA
  authors:
    - Your names
  extraProperties:
    inceptionYear: 2022

assemble:
  enabled: false

release:
  generic:
    owner: Your names
    host: https://example.com
    username: bamboo
    token: MyAwesomePassword
    changelog:
      #      links: true
      preset: 'conventional-commits'
      formatted: ALWAYS
      format: '- [{{commitTitle}}](http://bitbucket/scm/corsea/person.git/commits/{{commitFullHash}}) ({{commitAuthor}})'
      content: |
        # Release {{tagName}} 

        {{changelogChanges}}
        {{changelogContributors}}
      contributors:
        format: '- {{contributorName}}'
      labelers:
        - label: 'self-service'
          title: 'regex:develselfproservice'
      excludeLabels:
        - 'self-service'
      hide:
        uncategorized: true
        categories:
          - 'merge'
          - 'build'
          - 'tasks'
        contributors:
          - 'Development Self Provisioning Service'
          - 'gstragie'

announce:
  teams:
    active: ALWAYS
    webhook: https://gcloudbelgium.webhook.office.com/webhookb2/12b64c67-33ed-434d-9bac-4db6fa0e1529@32828042-1cc9-4b6b-9760-0b00812868a3/IncomingWebhook/1ed2091d779274a7c838ea2d38c19f6a/b10db6ab-5470-4c51-bb8e-2ca6d355124b
