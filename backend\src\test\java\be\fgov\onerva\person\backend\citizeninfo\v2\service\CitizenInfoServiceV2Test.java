package be.fgov.onerva.person.backend.citizeninfo.v2.service;

import backend.rest.model.CitizenInfoDTO;
import backend.rest.model.CitizenInfoPageV2DTO;
import backend.rest.model.CitizenInfoV2DTO;
import be.fgov.onerva.person.backend.citizeninfo.model.*;
import be.fgov.onerva.person.backend.citizeninfo.persistence.*;
import be.fgov.onerva.person.backend.citizeninfo.v2.exception.CitizenInfoException;
import be.fgov.onerva.person.backend.citizeninfo.v2.mapper.CitizenInfoMapperV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CitizenInfoServiceV2Test {
    @InjectMocks
    private CitizenInfoServiceV2 citizenInfoService;

    @Mock
    private CitizenInfoRepository mockInfoRepository;

    @Mock
    private CitizenInfoNationRepository mockNationRepository;

    @Mock
    private CitizenInfoGeneralRepository mockGeneralRepository;

    @Mock
    private CitizenInfoCommunicRepository mockCommunicRepository;

    @Mock
    private CitizenInfoMapperV2 mockCitizenInfoMapperV2;

    @Mock
    private CitizenInfoCompteRepository mockCompteInfoRepository;
    @Mock
    private CitizenInfoComuneDsRepository comuneDsRepository;

    private CitizenInfoEntity citizenInfoEntity1 = new CitizenInfoEntity(1,2,true,4,false);
    private CitizenInfoEntity citizenInfoEntity2 = new CitizenInfoEntity(5,6,false,8,true);


    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(citizenInfoService, "nationalCitizenDefault", 150);
    }

    @Test
    void getCitizenInfoByNumboxWithNumboxNull_error() {

        try {
            citizenInfoService.getCitizenInfoBySsinListV2(null,null);
            fail();
        } catch (CitizenInfoException e) {
            assertEquals("Ssin is required",e.getMessage());
        }

    }

    @Test
    void testGetCitizenInfoBySsinListWhenCitizenInfoIsEmpty_returnCitizenInfoPageDtoEmpty() {
        List<String> ssinList = Collections.emptyList();
        Pageable pageable = Pageable.unpaged();

        Page<CitizenInfoEntity> emptyPage = Page.empty();
        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(anyList(),anyInt(), any(Pageable.class))).thenReturn(emptyPage);

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testGetCitizenInfoBySsinListWhenCitizenInfoIsEmpty_returnCitizenInfoPageDtoNull() {
        List<String> ssinList = Collections.emptyList();
        Pageable pageable = Pageable.unpaged();

        Page<CitizenInfoEntity> emptyPage = null;
        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(anyList(),anyInt(), any(Pageable.class))).thenReturn(emptyPage);

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testGetCitizenInfoBySsinListWhenMapperReturnNull_returnCitizenInfoPageDtoEmpty() {
        List<String> ssinList = Collections.emptyList();
        Pageable pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(null);

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
    }
    @Test
    void testGetCitizenInfoBySsinListWhenMapperReturnContentNull_returnCitizenInfoPageDtoEmpty() {
        List<String> ssinList = Collections.emptyList();
        Pageable pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(null);

        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testGetCitizenInfoBySsinListWhenMapperReturnContentNull_returnCitizenInfoPageDto() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,0).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,0).build();

        var citizenInfoGeneralList = List.of(
                citizenInfoGeneral1,
                citizenInfoGeneral2
        );

        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(citizenInfoGeneralList);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
    }

    @Test
    void getCitizen_iban_found() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,0,0).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,0,0).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));

        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));


        var citizenInfoCompte = Optional.of(buildCitizenInfoCompte(1,"**********",20210101));

        when(mockCompteInfoRepository.findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyLong(), anyInt(), anyInt())).thenReturn(citizenInfoCompte);

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********",result.getContent().get(0).getIban());
    }

    @Test
    void getCitizen_iban_from_info_general() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,0).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,0).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));

        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
        when(mockInfoRepository.findByNumBoxIn(anyList())).thenReturn(List.of());

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
    }

    @Test
    void getCitizen_with_citizenInfoPageV2DTO_content_null() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(new ArrayList<>());
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertNull(result.getContent().get(0).getLastName());
        assertNull(result.getContent().get(0).getAddress());
        assertNull(result.getContent().get(0).getIban());
        assertEquals(new BigDecimal(123),result.getContent().get(0).getNumBox());
        assertNull(result.getContent().get(0).getOP());
    }

    @Test
    void getCitizen_with_citizen_info_comunic() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));
        citizenInfoDTO1.setSex(null);
        citizenInfoDTO1.setLanguage(null);
        citizenInfoDTO1.setDateMcpte(null);

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,0)
                .iban(null)
                .language(null)
                .build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,0)
                .iban("BE123")
                .sex(1)
                .language(1)
                .dateMCpte(99999999)
                .build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");
        var citizenInfoCommunic2 = buildCitizenInfoCommunic(456,1,null,null,null,null,null);

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(any(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1,citizenInfoCommunic2));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m", result.getContent().get(0).getSex());
        assertNull(result.getContent().get(0).getLanguage());

        assertEquals("m",result.getContent().get(1).getSex());
        assertEquals("fr",result.getContent().get(1).getLanguage());

        assertNull(result.getContent().get(0).getDateMcpte());
        assertNull(result.getContent().get(1).getDateMcpte());

        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("",result.getContent().get(0).getIban());
        assertEquals("BE123",result.getContent().get(1).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());

        assertNull(result.getContent().get(1).getGsmOnem());
        assertNull(result.getContent().get(1).getTelephoneOnem());
        assertNull(result.getContent().get(1).getTelephoneReg());
        assertNull(result.getContent().get(1).getGsmReg());
    }

    @Test
    void getCitizen_with_nation_NON_BELGE_citizen() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,1).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,1).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var buildCitizenInfoNation1 = buildCitizenInfoNation(1,123,10,1,20210101);
        var buildCitizenInfoNation2 = buildCitizenInfoNation(2,456,11,1,20210102);
        var buildCitizenInfoNation3 = buildCitizenInfoNation(3,123,12,1,20210103);

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(any(),anyInt(), anyInt())).thenReturn(List.of(buildCitizenInfoNation1,buildCitizenInfoNation2,buildCitizenInfoNation3));

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertEquals(BigDecimal.valueOf(12),result.getContent().get(0).getNationBcss());
        assertEquals(LocalDate.of(2021,01,03),result.getContent().get(0).getNationDateValid());
        assertEquals(BigDecimal.valueOf(12),result.getContent().get(0).getFlagNation());

        assertEquals(LocalDate.of(2021,01,02),result.getContent().get(1).getNationDateValid());
    }

    @Test
    void getCitizen_WITHOUT_nation() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,1).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,1).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(new ArrayList<>());

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertNull(result.getContent().get(0).getNationBcss());
        assertNull(result.getContent().get(0).getNationDateValid());
        assertNull(result.getContent().get(0).getFlagNation());
        assertNull(result.getContent().get(1).getNationDateValid());
    }

    @Test
    void getCitizen_with_nation_belga_citizen() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,0).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,0).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(new ArrayList<>());

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertNull(result.getContent().get(0).getNationBcss());
        assertNull(result.getContent().get(0).getNationDateValid());
        assertEquals(BigDecimal.valueOf(150),result.getContent().get(0).getFlagNation());

        assertNull(result.getContent().get(1).getNationDateValid());
    }

    @Test
    void getCitizen_with_nationWithInvalidDate() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,1).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,1).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var buildCitizenInfoNation1 = buildCitizenInfoNation(1,123,1,1,20000101);
        var buildCitizenInfoNation2 = buildCitizenInfoNation(2,456,1,1,20000101);

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(List.of(buildCitizenInfoNation1,buildCitizenInfoNation2));

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertEquals(BigDecimal.valueOf(1),result.getContent().get(0).getNationBcss());
        assertEquals(LocalDate.of(2000,1,1), result.getContent().get(0).getNationDateValid());
        assertEquals(BigDecimal.valueOf(1),result.getContent().get(0).getFlagNation());
    }

    @Test
    void getCitizen_with_comuneDs() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,1).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,1).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var buildCitizenInfoNation1 = buildCitizenInfoNation(1,123,1,1,20210101);
        var buildCitizenInfoNation2 = buildCitizenInfoNation(2,456,1,1,20210102);

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(List.of(buildCitizenInfoNation1,buildCitizenInfoNation2));

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(), anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var comuneDs1 = buildCitizenInfoComuneDs(1L,123,1,20210101,1);
        var comuneDs2 = buildCitizenInfoComuneDs(2L,456,1,20210102,1);

        when(comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(List.of(comuneDs1,comuneDs2));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertEquals(BigDecimal.valueOf(1),result.getContent().get(0).getNationBcss());
        assertEquals(LocalDate.of(2021,01,01),result.getContent().get(0).getNationDateValid());
        assertEquals(BigDecimal.valueOf(1),result.getContent().get(0).getFlagNation());
        assertEquals(LocalDate.of(2021,01,01),result.getContent().get(0).getCommuneDateValid());
    }

    @Test
    void getCitizen_with_dateMCpte() {

        var ssinList = Stream.of("123456","456789").toList();
        var pageable = Pageable.unpaged();

        var citizenInfoEntityList = new PageImpl(
                Stream.of(citizenInfoEntity1, citizenInfoEntity2).toList()
        );

        when(mockInfoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(
                anyList(),anyInt(), any(Pageable.class))
        ).thenReturn(citizenInfoEntityList);

        var citizenInfoDTO1 = buildCitizenInfoV2DTO(1L,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9", BigDecimal.valueOf(123),BigDecimal.valueOf(999));
        var citizenInfoDTO2 = buildCitizenInfoV2DTO(2L,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321", BigDecimal.valueOf(456),BigDecimal.valueOf(888));

        var content = List.of(
                citizenInfoDTO1,
                citizenInfoDTO2
        );

        var citizenInfoPageDtoReturn =  new CitizenInfoPageV2DTO();
        citizenInfoPageDtoReturn.setContent(content);

        var citizenInfoGeneral1 = buildCitizenInfoGeneral(1,"John,Doe","Rue de la Loi 16, 1000 Bruxelles","**********9",123,1,1)
                .dateMCpte(20210101).build();
        var citizenInfoGeneral2 = buildCitizenInfoGeneral(2,"Mary,Jane","Av de lala 12, 1000 Bruxelles","BE987654321",456,1,1)
                .dateMCpte(20210102).build();

        when(mockGeneralRepository.getCitizenInfoGeneralByNumBoxIn(anyList())).thenReturn(List.of(citizenInfoGeneral1,citizenInfoGeneral2));
        when(mockCitizenInfoMapperV2.mapPageToDto(any())).thenReturn(citizenInfoPageDtoReturn);
//        when(mockInfoRepository.getNumPensByNumBox(anyList())).thenReturn(List.of(1,2));

        var buildCitizenInfoNation1 = buildCitizenInfoNation(1,123,20,1,20210101);
        var buildCitizenInfoNation2 = buildCitizenInfoNation(2,456,21,1,20210102);

        when(mockNationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(List.of(buildCitizenInfoNation1,buildCitizenInfoNation2));

        var citizenInfoCommunic1 = buildCitizenInfoCommunic(123,1,"<EMAIL>","11111","2222","3333","4444");

        when(mockCommunicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(), anyInt(),anyInt())).thenReturn(List.of(citizenInfoCommunic1));

        var comuneDs1 = buildCitizenInfoComuneDs(1L,123,1,20210101,1);
        var comuneDs2 = buildCitizenInfoComuneDs(2L,456,1,20210102,1);

        when(comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(anyList(),anyInt(), anyInt())).thenReturn(List.of(comuneDs1,comuneDs2));

        var result = citizenInfoService.getCitizenInfoBySsinListV2(ssinList, pageable);

        assertNotNull(result);
        assertFalse(result.getContent().isEmpty());
        assertEquals(2,result.getContent().size());
        assertEquals("<EMAIL>",result.getContent().get(0).getEmail());
        assertEquals("123",result.getContent().get(0).getFlagPurge());
        assertEquals("m",result.getContent().get(0).getSex());
        assertEquals(BigDecimal.valueOf(100),result.getContent().get(0).getFlagVCpte());
        assertEquals("**********9",result.getContent().get(0).getIban());
        assertEquals("11111",result.getContent().get(0).getGsmOnem());
        assertEquals("2222",result.getContent().get(0).getTelephoneOnem());
        assertEquals("3333",result.getContent().get(0).getTelephoneReg());
        assertEquals("4444",result.getContent().get(0).getGsmReg());
        assertEquals(BigDecimal.valueOf(20),result.getContent().get(0).getNationBcss());
        assertEquals(LocalDate.of(2021,01,01),result.getContent().get(0).getNationDateValid());
        assertEquals(BigDecimal.valueOf(20),result.getContent().get(0).getFlagNation());
        assertEquals(LocalDate.of(2021,01,01),result.getContent().get(0).getCommuneDateValid());
        assertEquals(LocalDate.of(2021,01,01),result.getContent().get(0).getDateMcpte());
    }


//    BUILDERS

    private CitizenInfoComuneDs buildCitizenInfoComuneDs(
            Long id,
            Integer numBox,
            Integer flagValid,
            Integer dateValid,
            Integer rvaCountryCode
    ) {
        return CitizenInfoComuneDs.builder()
                .id(id)
                .numBox(numBox)
                .flagValid(flagValid)
                .dateValid(dateValid)
                .rvaCountryCode(rvaCountryCode)
                .build();
    }


    private CitizenInfoNation buildCitizenInfoNation(
            long id,
            int numBox,
            Integer nation,
            int flagValid,
            int dateValid
    ) {
        return CitizenInfoNation.builder()
                .id(id)
                .numBox(numBox)
                .nation(nation)
                .flagValid(flagValid)
                .dateValid(dateValid)
                .build();
    }
    private CitizenInfoCommunic buildCitizenInfoCommunic(
            Integer numbox,
            Integer flagValid,
            String email,
            String gsmOnem,
            String telephoneOnem,
            String telephoneReg,
            String gsmReg
    ){
        return CitizenInfoCommunic.builder()
                .numBox(numbox)
                .flagValid(flagValid)
                .email(email)
                .gsmOnem(gsmOnem)
                .telephoneOnem(telephoneOnem)
                .telephoneReg(telephoneReg)
                .gsmReg(gsmReg)
                .build();
    }
    private CitizenInfoCompte buildCitizenInfoCompte(
            long id,
            String iban,
            int dateValid
    ) {
        return CitizenInfoCompte.builder().id(id).iban(iban).dateValid(dateValid).flagValid(1).build();
    }
    private CitizenInfoDTO buildCitizenInfoDTO(
            Long id,
            String lastName,
            String address,
            String iban,
            BigDecimal numBox,
            BigDecimal numPens
    ) {
        var citizenInfoDTO = new CitizenInfoDTO();
        citizenInfoDTO.setId(id);
        citizenInfoDTO.setLastName(lastName);
        citizenInfoDTO.setAddress(address);
        citizenInfoDTO.setIban(iban);
        citizenInfoDTO.setNumBox(numBox);
        citizenInfoDTO.setNumPens(numPens);
        citizenInfoDTO.setSex("1");
        citizenInfoDTO.setEmail("<EMAIL>");
        citizenInfoDTO.setFlagVCpte(BigDecimal.valueOf(100));
        citizenInfoDTO.setDeceasedDate(LocalDate.of(2001,01,01));
        return citizenInfoDTO;
    }

    private CitizenInfoGeneral.CitizenInfoGeneralBuilder buildCitizenInfoGeneral(
            long id,
            String fullName,
            String address,
            String iban,
            Integer numBox,
            Integer flagVCpte,
            Integer flagNation
    ) {
        return CitizenInfoGeneral.builder()
                .id(id)
                .numBox(numBox)
                .iban(iban)
                .OP(123)
                .language(1)
                .address(address)
                .sex(1)
                .deceasedDate(20210101)
                .flagVCpte(flagVCpte == 1)
                .unemploymentOffice(211)
                .fullName(fullName)
                .zipCode(9999)
                .flagNation(flagNation == 1);
    }

    private CitizenInfoV2DTO buildCitizenInfoV2DTO(
            Long id,
            String lastName,
            String address,
            String iban,
            BigDecimal numBox,
            BigDecimal numPens
    ) {
        var citizenInfoDTO = new CitizenInfoV2DTO();
        citizenInfoDTO.setId(id);
        citizenInfoDTO.setLastName(lastName);
        citizenInfoDTO.setAddress(address);
        citizenInfoDTO.setIban(iban);
        citizenInfoDTO.setNumBox(numBox);
        citizenInfoDTO.setNumPens(numPens);
        citizenInfoDTO.setSex("1");
        citizenInfoDTO.setFlagPurge("123");
        citizenInfoDTO.setEmail("<EMAIL>");
        citizenInfoDTO.setFlagVCpte(BigDecimal.valueOf(100));
        citizenInfoDTO.setDeceasedDate(LocalDate.of(2001,01,01));
        return citizenInfoDTO;
    }

}
