package be.fgov.onerva.person.backend.config;

import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.flyway.FlywayDataSource;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Map;

@Configuration(proxyBeanMethods = false)
public class DatabaseConfig {

    @EnableJpaRepositories(
            basePackages = {"be.fgov.onerva.person.backend.citizen", "be.fgov.onerva.person.backend.citizeninfo"},
            entityManagerFactoryRef = "mfxEmf",
            transactionManagerRef= "mfxTransactionManager"
    )
    @Configuration(proxyBeanMethods = false)
    static class Mfx {

        @Bean
        @Primary
        @ConfigurationProperties("datasource.mfx")
        public DataSourceProperties mfxDataSourceProperties() {
            return new DataSourceProperties();
        }

        @Bean
        @Primary
        @ConfigurationProperties("datasource.mfx.hikari")
        public HikariDataSource mfxDataSource(DataSourceProperties mfxDataSourceProperties) {
                return mfxDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
        }

        @Primary
        @Bean
        public LocalContainerEntityManagerFactoryBean mfxEmf(EntityManagerFactoryBuilder builder, HikariDataSource mfxDataSource) {
            return builder
                    .dataSource(mfxDataSource)
                    .properties(Map.of("hibernate.dialect", "org.hibernate.dialect.SQLServerDialect"))
                    .packages(CitizenEntity.class, CitizenInfoEntity.class)
                    .build();
        }

        @Primary
        @Bean
        public PlatformTransactionManager mfxTransactionManager(LocalContainerEntityManagerFactoryBean mfxEmf) {
            return new JpaTransactionManager(mfxEmf.getObject());
        }
    }

    @EnableJpaRepositories(
            basePackages = "be.fgov.onerva.person.backend.request",
            entityManagerFactoryRef = "personEmf",
            transactionManagerRef= "personTransactionManager"
    )
    @Configuration(proxyBeanMethods = false)
    static class Person {

        @Bean
        @ConfigurationProperties("datasource.person")
        public DataSourceProperties personDataSourceProperties() {
            return new DataSourceProperties();
        }

        @FlywayDataSource
        @Bean
        @ConfigurationProperties("datasource.person.hikari")
        public HikariDataSource personDataSource(@Qualifier("personDataSourceProperties") DataSourceProperties personDataSourceProperties) {
            return personDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
        }

        @Bean
        public LocalContainerEntityManagerFactoryBean personEmf(EntityManagerFactoryBuilder builder, @Qualifier("personDataSource") HikariDataSource personDataSource) {
            return builder
                    .dataSource(personDataSource)
                    .packages(PersonRequest.class)
                    .build();
        }

        @Bean
        public PlatformTransactionManager personTransactionManager(@Qualifier("personEmf") LocalContainerEntityManagerFactoryBean personEmf) {
            return new JpaTransactionManager(personEmf.getObject());
        }
    }

}
