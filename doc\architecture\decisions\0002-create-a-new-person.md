# 2. create a new person

Date: 2023-09-11

## Status

Accepted

## Context

For u-benefit project we need to create a temporary citizen in our system. We need to create this citizen because we
want to have a process, representing article 60 flow, in WO. We need to create a temporary citizen, because real entry 
will be created when the citizen will request unemployment. In mainframe language we need to create a S01 of category 1.


## Decision

We decided to create this citizen in the mainframe, in keybox_ds table, for 2 reason:
 1) WO is currently plug to keybox_ds table (access via the person service)
 2) We keep the coherence of our current system

We decide to implement that functionality regarding the flow descirbe by the following diagram.

![](../../files/create-person.png)

## Consequences

We will offer the first possibility to create a citizen in the Mainframe database.