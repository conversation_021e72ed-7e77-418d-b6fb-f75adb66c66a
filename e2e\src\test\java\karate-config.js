function fn() {
    var env = karate.env; // get system property 'karate.env'
    karate.log('karate.env system property was:', env);
    if (!env) {
        env = 'dev';
    }
    var config = {
        env: env
    }
    if (env === 'dev') {
        baseUrl = 'http://localhost:8080'
        queueUrl = 'http://localhost:15672'
        auth= {
            url: "http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/token",
            clientSecret: 'person-secret'
        }
    } else if (env === 'ci') {
        baseUrl = 'https://person-e2e.test.paas.onemrva.priv'
        queueUrl = 'https://rabbitmq-person-e2e.test2.paas.onemrva.priv'
        auth= {
            url: "https://person-e2e-kc.test.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token",
            clientSecret: 'person-secret'
        }
    }
    config.baseUrl = baseUrl;
    config.queueUrl = queueUrl;
    config.auth = auth;
    return config;
}
