global:
  environment: val
  registry: docker-beta.onemrva.priv
  routes:
    host: person.val.paas.onemrva.priv

backend:
  springConfiguration:
    spring:
      security:
        oauth2:
          resource-server:
            jwt:
              issuer-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents
          client:
            provider:
              keycloak:
                issuer-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local

    wave:
      api: https://wo-configurator.val.paas.onemrva.priv/api

    datasource:
      mfx:
        url: ****************************************************************************************************************************************************************;
        username: CORSEA_Person_user
      person:
        url: ****************************************************************************************************************************************************
        username: PERSON_user

    ibm:
      mq:
        connName: crossval.onemrva.priv(1607)
        user: MQJAVAVAL@onemrva
        queueManager: QM_WEB_VAL
        channel: CHSRVC_WEB_UBENEFIT
        useAuthenticationMQCSP: false

    rabbitmq:
      oauth:
        enabled: true
        clientId: person-backend
        tokenEndpointUri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token

kcconfig:
  keycloak:
    url: https://keycloak.val.paas.onemrva.priv/
