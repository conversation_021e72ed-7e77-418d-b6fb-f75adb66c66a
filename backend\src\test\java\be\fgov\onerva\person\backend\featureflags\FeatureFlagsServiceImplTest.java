package be.fgov.onerva.person.backend.featureflags;

import com.flagsmith.FlagsmithClient;
import com.flagsmith.flagengine.features.FeatureModel;
import com.flagsmith.flagengine.features.FeatureStateModel;
import com.flagsmith.models.BaseFlag;
import com.flagsmith.models.Flags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FeatureFlagsServiceImplTest {
    @Mock
    private FlagsmithClient flagsmithClient;
    @InjectMocks
    private FeatureFlagsServiceImpl featureFlagsService;

    @Test
    void isFeatureEnabled() throws Exception {
        when(flagsmithClient.getEnvironmentFlags())
                .thenReturn(Flags.fromApiFlags(List.of(), null, null));

        assertThat(featureFlagsService.isFeatureEnabled("feat1")).isFalse();

        when(flagsmithClient.getEnvironmentFlags())
                .thenReturn(Flags.fromApiFlags(List.of(flag("feat1", true)), null, null));

        assertThat(featureFlagsService.isFeatureEnabled("feat1")).isTrue();
    }

    @Test
    void getFlag() throws Exception {
        when(flagsmithClient.getEnvironmentFlags())
                .thenReturn(Flags.fromApiFlags(List.of(), null, null));

        assertThat(featureFlagsService.getFlag("feat1")).isNull();

        when(flagsmithClient.getEnvironmentFlags())
                .thenReturn(Flags.fromApiFlags(List.of(flag("kill-switch", false)), null, null));

        BaseFlag flag = featureFlagsService.getFlag("kill-switch");
        assertThat(flag).isNotNull();
        assertThat(flag.getFeatureName()).isEqualTo("kill-switch");
        assertThat(flag.getEnabled()).isFalse();
    }

    FeatureStateModel flag(String name, boolean enabled) {
        FeatureModel feature = new FeatureModel();
        feature.setId(1);
        feature.setName(name);
        feature.setType("String");

        var fsm = new FeatureStateModel();
        fsm.setFeature(feature);
        fsm.setEnabled(enabled);

        return fsm;
    }
}