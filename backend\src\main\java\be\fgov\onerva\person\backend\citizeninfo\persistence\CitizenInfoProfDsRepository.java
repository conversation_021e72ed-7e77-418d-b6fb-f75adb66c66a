package be.fgov.onerva.person.backend.citizeninfo.persistence;

import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoProfDs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


public interface CitizenInfoProfDsRepository  extends JpaRepository<CitizenInfoProfDs, Integer>, JpaSpecificationExecutor<CitizenInfoProfDs> {

    List<CitizenInfoProfDs> findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List<Integer> numbox, int flagValid, int dateValid);
}
