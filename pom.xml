<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.4</version>
        <relativePath/>
    </parent>

    <groupId>be.fgov.onerva.person</groupId>
    <artifactId>parent</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>
    <name>Person Service</name>

    <modules>
        <module>backend</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <sonar.projectKey>d9838199-9eef-4934-b6ef-611285526a3a</sonar.projectKey>
        <sonar.token>****************************************</sonar.token>
        <sonar.host.url>https://sonarqubeprod.onemrva.priv</sonar.host.url>
        <sonar.ci.autoconfig.disabled>true</sonar.ci.autoconfig.disabled>
        <sonar.projectName>Person</sonar.projectName>
        <sonar.dependencyCheck.htmlReportPath>${project.build.directory}/dependency-check-report.html
        </sonar.dependencyCheck.htmlReportPath>
        <sonar.dependencyCheck.jsonReportPath>${project.build.directory}/dependency-check-report.json
        </sonar.dependencyCheck.jsonReportPath>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.coverage.exclusions>
            **/be/fgov/onerva/person/backend/config/**,
            **/be/fgov/onerva/person/backend/BackendApplication.java
        </sonar.coverage.exclusions>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <rules>
                        <rule>
                            <element>CLASS</element>
                            <limits>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.80</minimum>
                                </limit>
                                <limit>
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.75</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jreleaser</groupId>
                <artifactId>jreleaser-maven-plugin</artifactId>
                <version>1.17.0</version>
                <configuration>
                    <configFile>jreleaser.yml</configFile>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>sonar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <version>5.0.0.4389</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
