package be.fgov.onerva.person.backend.citizeninfo.v2.controller;

import backend.api.CitizenInfoV2Api;
import backend.rest.model.CitizenInfoPageV2DTO;
import be.fgov.onerva.person.backend.citizeninfo.v2.service.CitizenInfoServiceV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.springframework.http.ResponseEntity.ok;

@RequiredArgsConstructor
@RestController
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api")
public class CitizenInfoV2Controller implements CitizenInfoV2Api {

    private final CitizenInfoServiceV2 citizenInfoServiceV2;

    @Override
    public ResponseEntity<CitizenInfoPageV2DTO> searchCitizenInfov2(List<String> ssins,
                                                                    String dataReturned,
                                                                    Integer pageNumber,
                                                                    Integer pageSize) {
        log.debug("searchCitizenInfov2: ssins={}, dataReturned={}, pageNumber={}, pageSize={}", ssins, dataReturned, pageNumber, pageSize);
        var citizenInfo = citizenInfoServiceV2.getCitizenInfoBySsinListV2(ssins, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, "numPens")));
        log.debug("searchCitizenInfov2: found {} citizenInfo", (citizenInfo!=null && citizenInfo.getContent()!=null) ? citizenInfo.getContent().size() : 0);
        return ok(citizenInfo);
    }

}
