global:
  environment: ci
  routes:
    host: person-e2e.test.paas.onemrva.priv
  metrics:
    enabled: false
  logging:
    enabled: false
  traces:
    enabled: false

backend:
  image:
    registry: docker-alpha.onemrva.priv
  route:
    paths:
      - /api
      - /actuator
      - /e2e
  springConfiguration:
    spring:
      autoconfigure:
        exclude: org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
      security:
        oauth2:
          resource-server:
            jwt:
              issuer-uri: https://person-e2e-kc.test.paas.onemrva.priv/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq.person-ci.svc.cluster.local

    keycloak:
      checkToken: false

    datasource:
      mfx:
        url: *********************************************************
        username: sa
      person:
        url: ***********************************************************
        username: sa

    ibm:
      mq:
        autoConfigure: false

    queue:
      out: ${queue.in}

kcconfig:
  keycloak:
    url: https://person-e2e-kc.test.paas.onemrva.priv

infra:
  enabled: true

  keycloak:
    route:
      enabled: true
      host: person-e2e-kc.test.paas.onemrva.priv

  rabbitmq:
    route:
      enabled: true
      host: rabbitmq-person-e2e.test2.paas.onemrva.priv

karate:
  enabled: true
  env: ci

