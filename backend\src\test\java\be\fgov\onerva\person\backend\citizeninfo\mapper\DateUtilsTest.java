package be.fgov.onerva.person.backend.citizeninfo.mapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

public class DateUtilsTest {

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
     # int date,    expected
     NULL,          NULL
     0,             NULL
     123456789,     NULL
     1234567,       NULL
     20240229,      2024-02-29
     20230340,      NULL
     19000101,      1900-01-01
     """)
    public void convertToDate(Integer intDate, LocalDate expected) {
        assertThat(DateUtils.convertToDate(intDate)).isEqualTo(expected);
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
     # int date,    expected
     NULL,          NULL
     2024-02-29,    20240229
     1900-01-01,    19000101
     """)
    public void convertToDate(LocalDate date,  Integer expected) {
        assertThat(DateUtils.convertToInt(date)).isEqualTo(expected);
    }

    @Test
    public void currentDate() {
        String toDay = LocalDate.now().toString().replace("-", "");
        assertThat(DateUtils.currentDate()).isEqualTo(Integer.parseInt(toDay));
    }

}
