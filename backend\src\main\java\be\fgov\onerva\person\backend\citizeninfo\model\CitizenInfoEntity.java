package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.*;

import jakarta.persistence.*;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "keybox_ds", schema = "dbo")
public class CitizenInfoEntity {
    @Id
    @Column(name = "num_pens")
    private int numPens;

    private int numBox;
    private boolean flagPurge;
    private int lastId;

    private boolean flagToPurge;

}