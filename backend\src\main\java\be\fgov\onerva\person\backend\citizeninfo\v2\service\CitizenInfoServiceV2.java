package be.fgov.onerva.person.backend.citizeninfo.v2.service;

import backend.rest.model.CitizenInfoPageV2DTO;
import backend.rest.model.CitizenInfoV2DTO;
import backend.rest.model.LanguageDTO;
import backend.rest.model.SexDTO;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizeninfo.mapper.DateUtils;
import be.fgov.onerva.person.backend.citizeninfo.model.*;
import be.fgov.onerva.person.backend.citizeninfo.persistence.*;
import be.fgov.onerva.person.backend.citizeninfo.v2.exception.CitizenInfoException;
import be.fgov.onerva.person.backend.citizeninfo.v2.mapper.CitizenInfoMapperV2;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class CitizenInfoServiceV2 {

    private final CitizenInfoRepository infoRepository;
    private final CitizenInfoCompteRepository compteRepository;
    private final CitizenInfoGeneralRepository generalRepository;
    private final CitizenInfoNationRepository nationRepository;
    private final CitizenInfoCommunicRepository communicRepository;
    private final CitizenInfoMapperV2 citizenInfoMapperV2;
    private final CitizenInfoComuneDsRepository comuneDsRepository;

    @Value("${application.national-citizen-default}")
    private Integer nationalCitizenDefault;

    @Transactional
    public CitizenInfoPageV2DTO getCitizenInfoBySsinListV2(List<String> ssin, Pageable pageable) throws CitizenInfoException {

        log.debug("Getting citizen info by ssin list");

        if(Objects.isNull(ssin)){
            log.warn("Ssin is required");
            throw new CitizenInfoException("Ssin is required");
        }

        var citizenInfoPageV2 = new CitizenInfoPageV2DTO();
        var citizenInfo = getCitizenInfoOld(ssin, pageable);

        log.debug("Citizen info found {}",citizenInfo != null ? citizenInfo.stream().toList().size() : 0);

        var citizenInfoPageV2DTO = citizenInfoMapperV2.mapPageToDto(citizenInfo);

        if (Objects.isNull(citizenInfoPageV2DTO) || Objects.isNull(citizenInfoPageV2DTO.getContent())){
            log.warn("No citizen info found for ssin to mapper");
            return citizenInfoPageV2;
        }

        var ssinFormated= ssin.stream()
                .map(data -> StringUtils.leftPad(data,11,"0")).toList();
        log.debug("Ssin formated setted");

        var numboxList = citizenInfo.getContent()
                .stream()
                .map(CitizenInfoEntity::getNumBox)
                .toList();

        log.debug("Numbox list found {}",numboxList != null ? numboxList.size() : 0);

        var citizenInfoGeneralList = generalRepository.getCitizenInfoGeneralByNumBoxIn(numboxList);

        var bisNumbers = getBisNumbers(numboxList);

        var citizenInfoNation = nationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());
        var citizenInfoCommunics = communicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());
        var citizenInfoComuneDs = comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());

        citizenInfoPageV2DTO.getContent().forEach(citizenInfoV2DTO -> {
            citizenInfoV2DTO.setSsin(
                    StringUtils.leftPad(
                            PensionNumberUtils.convertToInssWithDefault(citizenInfoV2DTO.getNumPens().intValue()).toString(),
                            11,
                            '0')
            );
            log.debug("Setting citizen ssin");

            var citizenInfoGeneralFound = citizenInfoGeneralList
                    .stream()
                    .filter(i -> Objects.equals(i.getNumBox(), citizenInfoV2DTO.getNumBox().intValue()))
                    .findFirst();

            citizenInfoGeneralFound.ifPresentOrElse(
                    infoGeneral -> this.updateCitizenInfo(
                            citizenInfoV2DTO,
                            infoGeneral,
                            citizenInfoNation,
                            citizenInfoCommunics,
                            bisNumbers,
                            citizenInfoComuneDs
                    ),
                    () -> this.clearCitizenInfo(citizenInfoV2DTO)
            );
        });

        log.debug("Citizen info updated");

        return citizenInfoPageV2DTO;
    }

    private void updateCitizenInfo(CitizenInfoV2DTO citizenInfoDTO,
                                   CitizenInfoGeneral infoGeneral,
                                   List<CitizenInfoNation> citizenInfoNation,
                                   List<CitizenInfoCommunic> citizenInfoCommunics,
                                   Map<Integer,List<String>> bisNumbers,
                                   List<CitizenInfoComuneDs> citizenInfoComuneDs
    ) {

        log.debug("Updating citizen info");
        setBasicInfo(citizenInfoDTO, infoGeneral);
        setIbanInfo(citizenInfoDTO, infoGeneral);
        setSexAndLanguageInfo(citizenInfoDTO, infoGeneral);
        setNationInfo(citizenInfoDTO, infoGeneral, citizenInfoNation);
        setCommunicationInfo(citizenInfoDTO, infoGeneral, citizenInfoCommunics);
        setBisNumbers(citizenInfoDTO, bisNumbers);
        setDeceasedDate(citizenInfoDTO, infoGeneral.getDeceasedDate());
        setMcpteDate(citizenInfoDTO, infoGeneral.getDateMCpte());
        setCitizenInfoComuneDs(citizenInfoDTO, infoGeneral,citizenInfoComuneDs);

        citizenInfoDTO.setUnemploymentOffice(BigDecimal.valueOf(infoGeneral.getUnemploymentOffice()));
        citizenInfoDTO.setOP(BigDecimal.valueOf(infoGeneral.getOP()));
    }

    private void setCitizenInfoComuneDs(CitizenInfoV2DTO citizenInfoDTO,
                                        CitizenInfoGeneral infoGeneral,
                                        List<CitizenInfoComuneDs> citizenInfoComuneDs) {
        if (!citizenInfoComuneDs.isEmpty()) {
            log.debug("Citizen Info comuneDs exists");
            var comuneDs = citizenInfoComuneDs.stream()
                    .filter(comune -> comune.getNumBox() == infoGeneral.getNumBox())
                    .findFirst();
            log.debug("Setting citizen commune bcss");
            comuneDs.ifPresent(citizenInfoComuneDs1 -> {
                citizenInfoDTO.setCommuneDateValid(DateUtils.convertToDate(citizenInfoComuneDs1.getDateValid()));
                citizenInfoDTO.setRvaCountryCode(citizenInfoComuneDs1.getRvaCountryCode() > 9999 ? 1 : citizenInfoComuneDs1.getRvaCountryCode());
            });
        }
    }

    private void setMcpteDate(CitizenInfoV2DTO citizenInfoDTO, Integer dateMCpte) {
        if (dateMCpte != null && dateMCpte != 99999999) {
            log.debug("Citizen dateMCpte exists");
            citizenInfoDTO.setDateMcpte(DateUtils.convertToDate(dateMCpte));
        }
    }

    private void setBasicInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
        log.debug("Setting citizen basic info");
        citizenInfoDTO.setAddress(infoGeneral.getAddress().trim());
        citizenInfoDTO.setPostalCode(infoGeneral.getZipCode() == null? null : Integer.toString(infoGeneral.getZipCode()));
        var nameParts = infoGeneral.getFullName().split(",");
        citizenInfoDTO.setLastName(nameParts[0].trim());
        citizenInfoDTO.setFirstName(nameParts[1].trim());
        log.debug("Citizen basic info setted");
    }

    private void setIbanInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
        citizenInfoDTO.setIban(infoGeneral.getIban() != null ? infoGeneral.getIban().trim() : "");
        if (!infoGeneral.isFlagVCpte()) {
            log.debug("Citizen flagVCpte is 0");
            var iban = compteRepository.findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(infoGeneral.getId(), 9, DateUtils.currentDate())
                    .map(CitizenInfoCompte::getIban)
                    .orElse("")
                    .trim();
            citizenInfoDTO.setIban(iban);
        }
    }

    private void setSexAndLanguageInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
        citizenInfoDTO.setSex(SexDTO.fromValue(infoGeneral.getSex()).name());
        if (infoGeneral.getLanguage() != null) {
            citizenInfoDTO.setLanguage(LanguageDTO.fromValue(infoGeneral.getLanguage()).name());
        }
    }

    private void setNationInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral, List<CitizenInfoNation> citizenInfoNation) {

        if (!citizenInfoNation.isEmpty()) {
            log.debug("Citizen nation info exists");

            var citizenNation = citizenInfoNation.stream()
                    .filter(nationInfo -> Objects.equals(nationInfo.getNumBox(), infoGeneral.getNumBox()))
                    .max(Comparator.comparingInt(CitizenInfoNation::getDateValid));

            if (citizenNation.isPresent()) {
                log.debug("Setting citizen nation info from nation_ds");

                var nationBcss = BigDecimal.valueOf(
                        citizenNation.get().getNation() == null ? nationalCitizenDefault : citizenNation.get().getNation()
                );

                citizenInfoDTO.setNationBcss(nationBcss);
                citizenInfoDTO.setFlagNation(nationBcss);

                citizenInfoDTO.setNationDateValid(
                        DateUtils.convertToDate(citizenNation.get().getDateValid())
                );
                return;
            }
        }

        if (!infoGeneral.isFlagNation()) {
            log.debug("Citizen is Belgian based on flag_nation in general_ds");
            citizenInfoDTO.setNationBcss(null);
            citizenInfoDTO.setFlagNation(BigDecimal.valueOf(nationalCitizenDefault));
        }

    }

    private void setCommunicationInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral, List<CitizenInfoCommunic> citizenInfoCommunics) {
        log.debug("Citizen Info communication info exists");
        citizenInfoCommunics.stream()
                .filter(communic -> Objects.equals(communic.getNumBox(), infoGeneral.getNumBox()))
                .findFirst()
                .ifPresent(citizenInfoCommunic -> setCommunicInfoOld(citizenInfoDTO, citizenInfoCommunic));
    }

    private void setBisNumbers(CitizenInfoV2DTO citizenInfoDTO, Map<Integer,List<String>> bisNumbers) {
        var inssHistory = bisNumbers.get(citizenInfoDTO.getNumBox().intValue());
        if (inssHistory != null && !inssHistory.isEmpty()) {
            log.debug("Citizen Info ssin history found");
            citizenInfoDTO.setBisNumber(inssHistory);
        }
    }

    private void setDeceasedDate(CitizenInfoV2DTO citizenInfoDTO, Integer deceasedDate) {
        citizenInfoDTO.setDeceasedDate(null);
        if (deceasedDate != null && deceasedDate != 99999999) {
            log.debug("Citizen Info deceased date exists");
            citizenInfoDTO.setDeceasedDate(DateUtils.convertToDate(deceasedDate));
        }
    }

    private void clearCitizenInfo(CitizenInfoV2DTO citizenInfoDTO) {
        log.info("No citizen info found for ssin. Building empty citizen info");
        citizenInfoDTO.setAddress(null);
        citizenInfoDTO.setLastName(null);
        citizenInfoDTO.setFirstName(null);
        citizenInfoDTO.setDeceasedDate(null);
        citizenInfoDTO.setIban(null);
        citizenInfoDTO.setUnemploymentOffice(null);
        citizenInfoDTO.setOP(null);
    }

    private static void setCommunicInfoOld(CitizenInfoV2DTO el, CitizenInfoCommunic citizenInfoCommunic) {
        if(citizenInfoCommunic.getEmail()!= null){
            log.debug("Citizen Info email exists");
            el.setEmail(citizenInfoCommunic.getEmail().trim());
        }
        if(citizenInfoCommunic.getTelephoneOnem()!= null){
            log.debug("Citizen Info telephoneOnem exists");
            el.setTelephoneOnem(citizenInfoCommunic.getTelephoneOnem().trim());
        }
        if(citizenInfoCommunic.getTelephoneReg()!= null){
            log.debug("Citizen Info telephoneReg exists");
            el.setTelephoneReg(citizenInfoCommunic.getTelephoneReg().trim());
        }
        if(citizenInfoCommunic.getGsmOnem()!= null){
            log.debug("Citizen Info gsmOnem exists");
            el.setGsmOnem(citizenInfoCommunic.getGsmOnem().trim());
        }
        if(citizenInfoCommunic.getGsmReg()!= null){
            log.debug("Citizen Info gsmReg exists");
            el.setGsmReg(citizenInfoCommunic.getGsmReg().trim());
        }
    }

    private Page<CitizenInfoEntity> getCitizenInfoOld(List<String> ssin, Pageable pageable) {
        var numpens = ssin.stream().map(el -> PensionNumberUtils.convertFromInss(Long.valueOf(el))).toList();
        log.debug("NumPens V2 {}",numpens);
        var infos =  infoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(numpens, 9, pageable);
        log.debug("Get citizen info by numpens found: {} quantity", (infos != null && infos.get() != null) ? infos.get().collect(Collectors.toList()).size() : 0);
        return infos;
    }

    private Map<Integer,List<String>> getBisNumbers(List<Integer> box) {
        return infoRepository.findByNumBoxIn(box).stream()
                .filter(info -> info.getLastId() != 9)
                .collect(Collectors.groupingBy(
                        CitizenInfoEntity::getNumBox,
                        Collectors.mapping(info -> String.format("%011d", PensionNumberUtils.convertToInssWithDefault(info.getNumPens())), Collectors.toList())
                ));
    }

}
