# Person

[![Quality Gate Status](http://sonarqubeprod/api/project_badges/measure?project=d9838199-9eef-4934-b6ef-611285526a3a&metric=alert_status&token=53e95964c05b3a8272a904f31691c44afafa4588)](http://sonarqubeprod/dashboard?id=d9838199-9eef-4934-b6ef-611285526a3a) [![Security Rating](http://sonarqubeprod/api/project_badges/measure?project=d9838199-9eef-4934-b6ef-611285526a3a&metric=security_rating&token=53e95964c05b3a8272a904f31691c44afafa4588)](http://sonarqubeprod/dashboard?id=d9838199-9eef-4934-b6ef-611285526a3a) [![Coverage](http://sonarqubeprod/api/project_badges/measure?project=d9838199-9eef-4934-b6ef-611285526a3a&metric=coverage&token=53e95964c05b3a8272a904f31691c44afafa4588)](http://sonarqubeprod/dashboard?id=d9838199-9eef-4934-b6ef-611285526a3a)[![Technical Debt](http://sonarqubeprod/api/project_badges/measure?project=d9838199-9eef-4934-b6ef-611285526a3a&metric=sqale_index&token=53e95964c05b3a8272a904f31691c44afafa4588)](http://sonarqubeprod/dashboard?id=d9838199-9eef-4934-b6ef-611285526a3a)


## Purpose

-- _Insert here the purpose of this project_ --

## Global

For local development, we use skaffold to build and deploy in a local kubernetes.

## Required Tools
### Backend

The backend is developed using Spring Boot


- JDK 21 (currently we use the OpenJDK distribution, but any distribution should be ok)
- Maven 3.x 

### Running backend locally
1. Run the whole project on your local k8s cluster (minikube)
    ```
    skaffold dev --skip-tests
    ```
    The port forwardings are shown after a successful deploy to access the services locally.


2. Run the infrastructure (keycloak, rabbitmq, db, ...) needed by the project
    ```
    skaffold dev --skip-tests -m infra
    ```
    After the infra is running, the Spring Boot app can be launched in two ways:
   1. Use skaffold to run the app in another release
        ```
        skaffold dev --skip-tests -m app      
        ```
   2. Start the app in your favorite IDE or via Maven cli. The local profile will be activated.
      This can be useful when developing locally to speed up a bit.
        ```
        mvn -pl backend spring-boot:run      
        ```
### Running the e2e tests locally
First run the backend locally ... see above.

Run KarateRunnerTest in your IDE or via Maven...
```
cd e2e
mvn test      
```


## Project composition

This project has the following components & tools:

* backend - SPRING_BOOT
* Skaffold
* Helm
* OpenAPI

## Building local
Use profile to build the project locally by maven
```
mvn clean install -Plocal
```

## Remote debugging
Create a remote JVM debugging configuration in Intellij Idea Debugger mode: attached to JVM Transport: socket Host: localhost Port: 5005


## If you cannot start the Java Unit test

```
cd /etc/systemd/system/
sudo mkdir docker.service.d
cd docker.service.d
sudo nano http-proxy.conf
```

Content of the file: 
```
[Service]
Environment="HTTP_PROXY=http://proxygate.onemrva.priv:8888"
Environment="HTTPS_PROXY=http://proxygate.onemrva.priv:8888"
Environment="NO_PROXY=bitbucket,localhost,.localhost,.onemrva.priv,.rvadc.be,*********/12,************/24,************/24,************/24"
```

To save and close
 1) ctrl x
 2) Y
 3) Enter
