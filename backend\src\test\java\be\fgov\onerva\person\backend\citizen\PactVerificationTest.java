package be.fgov.onerva.person.backend.citizen;

import au.com.dius.pact.provider.junit5.HttpTestTarget;
import au.com.dius.pact.provider.junit5.PactVerificationContext;
import au.com.dius.pact.provider.junitsupport.IgnoreMissingStateChange;
import au.com.dius.pact.provider.junitsupport.Provider;
import au.com.dius.pact.provider.junitsupport.State;
import au.com.dius.pact.provider.junitsupport.loader.PactBroker;
import au.com.dius.pact.provider.spring.junit5.PactVerificationSpringProvider;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.IntegrationTest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestTemplate;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.server.LocalServerPort;

import java.util.Map;
import java.util.Random;


@Provider("Person")
@PactBroker(url = "https://pact-broker.prod.paas.onemrva.priv/", enablePendingPacts = "true", providerTags = {"main", "develop"})
@IgnoreMissingStateChange
public class PactVerificationTest extends IntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private CitizenRepository citizenRepository;

    @BeforeEach
    void setup(PactVerificationContext context) {
        context.setTarget(new HttpTestTarget("localhost", port));
    }

    @AfterEach
    void tearDown() {
        citizenRepository.deleteAll();
    }

    @State({"A citizen in mainframe database", "I search for an existing \"Person\" for a given query"})
    Map<String, String> aCitizenInMainframeDatabase() {
        var citizen = CitizenEntity.builder()
                .id(899111298)
                .zipCode(1000)
                .numBox(5837322)
                .fullName("Jean, Dupond")
                .build();
        citizenRepository.save(citizen);
        return Map.of("niss", "89111129857");
    }

    @State({"Multiple citizens in mainframe database"})
    Map<String, Object> multipleCitizenInMainframeDatabase() {
        for (int i = 0; i < 10 ; ++i) {
            var niss = generateNissBefore2000();
            var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(niss));
            var citizen = CitizenEntity.builder()
                    .id(numpens)
                    .zipCode(1000)
                    .numBox(generateRandomInt(100000, 999999))
                    .fullName("Jean, Dupond")
                    .build();
            citizenRepository.save(citizen);
        }
        return Map.of("denomination", "Dupond", "pageNumber", "0", "pageSize", "10");
    }

    public static String generateNissBefore2000() {
        var birthYear = generateRandomInt(60,99);
        var birthMonth = generateRandomInt(1,12);
        var birthDay = generateRandomInt(1,28);
        var nissBirthCount = generateRandomInt(100,999);
        var nissNumber = (birthYear * 10000000) + (birthMonth * 100000) + (birthDay * 1000) + nissBirthCount;
        var nissCheck = 97 - (nissNumber % 97);
        var nissCheckStr = nissCheck < 10 ? "0" + nissCheck : nissCheck;
        return nissNumber + "" + nissCheckStr;
    }

    private static int generateRandomInt(int low, int high) {
        var random = new Random();
        return random.nextInt(high - low) + low;
    }

    @State("A citizen not found in the mainframe database")
    Map<String, String> aCitizenNotInMainframeDatabase() {
        return Map.of("niss", "12312312373");
    }


    @TestTemplate
    @ExtendWith(PactVerificationSpringProvider.class)
    void pactVerificationTestTemplate(PactVerificationContext context) {
        context.verifyInteraction();
    }

}
