package be.fgov.onerva.person.backend.request.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class PersonMfxResponseTest {

    @Test
    void parseCreateV1() {
        var response = PersonMfxResponse.from("DOE,JANE                      700101002870000000000000000123+0001");
        assertThat(response.isV1()).isTrue();
        assertThat(response.getId()).isEqualTo(123);
        assertThat(response.getType()).isSameAs(PersonRequestType.CREATE);
        assertThat(response.getInss()).isEqualTo("70010100287");
        assertThat(response.getNames()).isEqualTo("DOE,JANE");
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getErrorMessage()).isNull();
        assertThat(response.getErrorCode()).isEqualTo(1);
    }

    @ParameterizedTest
    @CsvSource(textBlock = """
        # Code,  Request type
        0001,   CREATE
        0002,   UPDATE
        """)
    void parseV2(String prefix, PersonRequestType type) {
        var response = PersonMfxResponse.from(prefix + "0000000000000000666LE HERO,TOTO                  7001010028700000");
        assertThat(response.isV1()).isFalse();
        assertThat(response.getId()).isEqualTo(666);
        assertThat(response.getType()).isSameAs(type);
        assertThat(response.getInss()).isEqualTo("70010100287");
        assertThat(response.getNames()).isEqualTo("LE HERO,TOTO");
    }

    @ParameterizedTest
    @CsvSource(quoteCharacter = '"', textBlock = """
        # Code,  Error msg
        "-0001", "NISS must be numeric"
        "-0002", "Check digit NISS not correct"
        "-0003", "Functional code must be: 0001 or 0002"
        "-0004", "The person must be known in MFX (keybox_ds)"
        "-0300", "VALUE DATE is not a valid date"
        "-0666", "Unknown code: -666"
        "-1003", "The derived pension number from NISS must be known in MFX (keybox_ds)"
        "-1005", "Data inconsistency in MFX : the person is known in keybox_ds but not in general_ds"
        "-1100", "The street name is mandatory"
        "-1101", "The zip code is mandatory"
        "-1102", "The zip code must be known in the lookup table"
        "-1103", "The NIS code of the new address has been found, but the unemployment office number for this address is not available in the lookup table"
        "-1104", "The union due must be either true or false"
        "-1105", "Data inconsistency in MFX: the person is not found in general_ds."
        "-1106", "The payment type is not correct"
        "-1108", "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds."
        "-1110", "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds."
        "-1111", "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds."
        "-1112", "The nationality code must be known in the lookup table"
        "-1113", "The NIS code of the new address must be linked to the same unemployement office than the previous address"
        "-9105", "Technical error in MFX"
        "-9003", "Technical error in MFX"
        """)
    void errorUpdateMessage(String error, String msg) {
        var response = PersonMfxResponse.from("00020000000000000000666LE HERO,TOTO                  70010100287" + error);
        assertThat(response.isV1()).isFalse();
        assertThat(response.getErrorMessage()).isEqualTo(msg);
        assertThat(response.isSuccess()).isFalse();
    }

    @ParameterizedTest
    @CsvSource(textBlock = """
        # Code, Error code, Expected
        0001,   +0001,      true
        0001,   -0002,      true
        0001,   -0001,      false
        0002,   00000,      true
        0002,   -0002,      false
        """)
    void success(String prefix, String code, boolean expected) {
        var response = PersonMfxResponse.from(prefix + "0000000000000000666LE HERO,TOTO                  70010100287" + code);
        assertThat(response.isV1()).isFalse();
        assertThat(response.getId()).isEqualTo(666);
        assertThat(response.getInss()).isEqualTo("70010100287");
        assertThat(response.isSuccess()).isEqualTo(expected);
    }
}