package be.fgov.onerva.person.backend.request.formatter;

import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests specifically for uppercase conversion and accent stripping in the
 * MainframeUpdateMessageFormatter.
 */
@ExtendWith(MockitoExtension.class)
public class MainframeUpdateMessageFormatterUppercaseTest {

        @InjectMocks
        private MainframeUpdateMessageFormatter formatter;

        @Test
        @DisplayName("Should convert address to uppercase and strip accents")
        void formatUpdateMessage_withAccentedAddress_shouldConvertToUppercaseAndStripAccents() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(123456789L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 11, 15))
                                .address(Address.builder()
                                                .street("Rue de la Paix")
                                                .number("123")
                                                .box("Boîte A")
                                                .zip("1000")
                                                .city("Bruxelles")
                                                .countryCode(1)
                                                .build())
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                // Extract the address part (positions 46-76)
                String addressPart = result.substring(46, 76);

                // Verify uppercase conversion and accent stripping
                assertThat(addressPart.trim()).isEqualTo("RUE DE LA PAIX 123 BTE A");

                // Verify city is also uppercase and accents stripped
                String cityPart = result.substring(86, 116);
                assertThat(cityPart).contains("BRUXELLES");
        }

        @Test
        @DisplayName("Should convert account holder to uppercase and strip accents")
        void formatUpdateMessage_withAccentedAccountHolder_shouldConvertToUppercaseAndStripAccents() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(123456789L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 11, 15))
                                .address(Address.builder()
                                                .street("Main Street")
                                                .zip("1000")
                                                .city("Brussels")
                                                .build())
                                .accountHolder("José Martínez")
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                // Bank part starts at position 140
                int bankStart = 140;

                // Extract the account holder part (positions 140+45 to 140+75)
                String accountHolderPart = result.substring(bankStart + 45, bankStart + 75);

                // Verify uppercase conversion and accent stripping
                assertThat(accountHolderPart).contains("JOSE MARTINEZ");
        }

        @Test
        @DisplayName("Should replace 'BOITE' with 'BTE' in box field")
        void formatUpdateMessage_withBoiteInBox_shouldReplaceToBte() {
                // Given
                PersonRequest request = PersonRequest.builder()
                                .id(123456789L)
                                .type(PersonRequestType.UPDATE)
                                .niss("***********")
                                .valueDate(LocalDate.of(2024, 11, 15))
                                .address(Address.builder()
                                                .street("Rue de la Paix")
                                                .number("42")
                                                .box("BOITE 7")
                                                .zip("1000")
                                                .city("Brussels")
                                                .build())
                                .build();

                // When
                String result = formatter.formatUpdateMessage(request);

                // Then
                // Extract the address part (positions 46-76)
                String addressPart = result.substring(46, 76);

                // Verify "BOITE" is replaced with "BTE"
                assertThat(addressPart).contains("RUE DE LA PAIX 42 BTE 7");
        }
}
