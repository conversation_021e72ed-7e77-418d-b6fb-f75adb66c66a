package be.fgov.onerva.person.backend.config.props;

import com.rabbitmq.client.impl.OAuth2ClientCredentialsGrantCredentialsProvider;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = RabbitmqOauthProperties.PREFIX)
@Validated
@Getter
@Setter
public class RabbitmqOauthProperties {

    public static final String PREFIX = "rabbitmq.oauth";

    @NotBlank
    private String tokenEndpointUri;
    @NotBlank
    private String clientId;
    @NotBlank
    private String clientSecret;
    @NotBlank
    private String grantType = "client_credentials";
    private boolean enabled;

    public OAuth2ClientCredentialsGrantCredentialsProvider toCredentialsProvider() {
        return new OAuth2ClientCredentialsGrantCredentialsProvider.OAuth2ClientCredentialsGrantCredentialsProviderBuilder()
                .tokenEndpointUri(tokenEndpointUri)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType(grantType)
                .build();
    }
}
