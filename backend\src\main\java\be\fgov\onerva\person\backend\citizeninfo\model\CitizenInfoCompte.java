package be.fgov.onerva.person.backend.citizeninfo.model;

import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "cpte_ds", schema = "dbo")
public class CitizenInfoCompte {
    @Id
    private long id;

    private long parentId;

    private int flagValid;

    private int dateValid;

    @Column(name = "cpte_iban")
    private String iban;

    @Column(name = "cpte_bic")
    private String bic;

    @Column(name = "cpte_tit")
    private String bankAccountHolder;

}
