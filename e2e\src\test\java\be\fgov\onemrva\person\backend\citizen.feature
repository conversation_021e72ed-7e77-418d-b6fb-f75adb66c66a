Feature: citizen

  Background:
    Given url baseUrl
    And def tokenResponse = callonce read('auth.feature@getAccessToken')
    And header Authorization = "Bearer " + tokenResponse.response.access_token

  Scenario: Get citizen by niss when niss does not exist should return status 404 not found
    Given path "/api/citizen/22100200625"
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method GET
    Then status 404

  Scenario: Get citizen by niss when niss exists should return status 200 OK with the citizen data as json
    Given path "/api/citizen/88123180897"
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method GET
    Then status 200
    And match response contains {"niss":"88123180897","firstname":"NAME1","lastname":"LASTNAME1","numbox":991}

  Scenario: Get citizen by denomination when citizen exists should return status 200 OK with the citizen data as json
    Given path "/api/citizen/"
    And param query = 'fullName==^LASTNAME1*;fullName=ilike=NA;fullName=ilike=STNAME1'
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method GET
    Then status 200
    And match response contains deep {"content":[{"niss":"88123180897","firstname":"NAME1","lastname":"LASTNAME1","numbox":991}],"pageNumber":0,"pageSize":10,"totalPage":1,"totalElements":1,"isFirst":true,"isLast":true}

  Scenario: Get citizen by denomination when citizen does not exist should return status 200 OK with empty json
    Given path "/api/citizen/"
    And param query = 'fullName==^andwz*'
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method GET
    Then status 200
    And match response == {"content":[],"pageNumber":0,"pageSize":10,"totalPage":0,"totalElements":0,"isFirst":true,"isLast":true}

  Scenario: Get citizen by denomination ignoring case when citizen exists should return status 200 OK with the citizen data as json
    Given path "/api/citizen/"
    And param query = 'fullName==^LASTNAME1*;fullName=ilike=nA;fullName=ilike=STNaMe1'
    And header Content-Type = 'application/json'
    And header Accept = 'application/json'
    When method GET
    Then status 200
    And match response contains deep {"content":[{"niss":"88123180897","firstname":"NAME1","lastname":"LASTNAME1","numbox":991}],"pageNumber":0,"pageSize":10,"totalPage":1,"totalElements":1,"isFirst":true,"isLast":true}
