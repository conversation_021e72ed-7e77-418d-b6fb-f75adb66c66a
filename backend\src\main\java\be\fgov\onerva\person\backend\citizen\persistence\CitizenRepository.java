package be.fgov.onerva.person.backend.citizen.persistence;

import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CitizenRepository extends JpaRepository<CitizenEntity, Integer>, JpaSpecificationExecutor<CitizenEntity> {

    List<CitizenEntity> findByNumBox(Integer numbox);

}
