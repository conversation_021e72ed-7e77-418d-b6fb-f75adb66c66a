package be.fgov.onerva.person.backend.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.*;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class OpenApiConfig {
    @Bean
    OpenAPI customOpenAPI(OAuth2ResourceServerProperties props, Environment env) {
        var schemeName = "openId";
        String issuerUri = env.matchesProfiles("dev")? "http://localhost:8082/realms/onemrva-agents" : props.getJwt().getIssuerUri();
        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes(
                                schemeName,
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.OAUTH2)
                                        .flows(new OAuthFlows().authorizationCode(new OAuthFlow()
                                                .authorizationUrl(issuerUri + "/protocol/openid-connect/auth")
                                                .tokenUrl(issuerUri + "/protocol/openid-connect/token")
                                                .scopes(new Scopes().addString("openid", ""))
                                        ))
                        )
                )
                .addSecurityItem(new SecurityRequirement().addList(schemeName))
        ;
    }
}
