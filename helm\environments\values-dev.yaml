global:
  environment: dev
  routes:
    enabled: false
  metrics:
    enabled: false
  logging:
    enabled: false
  traces:
    enabled: false

backend:
  servicePort:
    port: 8080
  springConfiguration:
    spring:
      autoconfigure:
        exclude: org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
      security:
        oauth2:
          resource-server:
            jwt:
              issuer-uri: http://keycloak-http/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq

    keycloak:
      checkToken: false

    datasource:
      mfx:
        url: ***************************************************************
        username: sa
      person:
        url: *****************************************************************
        username: sa

    ibm:
      mq:
        autoConfigure: false

    queue:
      out: ${queue.in}

    logging:
      level:
        be:
          fgov:
            onerva:
              person:
                backend: debug


infra:
  enabled: true

kcconfig:
  realm:
    name: onemrva-agents
    clients:
      swagger:
        description: Public client for Person API
        public: true
        standardFlowEnabled: true
        implicitFlowEnabled: true
        redirectUris:
          - http://localhost*
        webOrigins:
          - '*'
    users:
      jdoe:
        email: <EMAIL>
        firstName: John
        lastName: Doe
        password: password
        attributes:
          language:
            - nl