package be.fgov.onerva.person.backend.request.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class PersonRequestTest {

    @ParameterizedTest
    @CsvSource(textBlock = """
        # Firstname,  Lastname,                                Expected
        '',           '',                                      ',                             '
        <PERSON>,    <PERSON>,                                  'MART<PERSON>,ROBERT C.              '
        <PERSON>,          <PERSON>, 'FERNANDEZ LEAL LUNA DELG,<PERSON><PERSON><PERSON>  <PERSON>
        <PERSON>,	  <PERSON>,        'RODRIGUEZ DE LA PENA Y D,ALEJA'
        ' <PERSON>', '	  <PERSON> ',                         'RODRIGUEZ,ALEJANDRO           '
        """)
    void formatNames(String first, String last, String expected) {
        var names = PersonRequest.formatNames(first, last).toString();
        assertThat(names.length()).isEqualTo(PersonRequest.NAMES_LENGTH);
        assertThat(names).isEqualTo(expected);
    }

    @ParameterizedTest
    @CsvSource(textBlock = """
        # Name,       Length,         Expected
        '',            5,             ''
        <PERSON>,     4,             ALEX
        Jérôme,        10,            JEROME
        """)
    void toUpper(String name, int length, String expected) {
        assertThat(PersonRequest.toUpper(name, length)).isEqualTo(expected);
    }

    @Test
    void trimInBuilderAndRecord() {
        PersonRequest req = PersonRequest.builder().type(PersonRequestType.CREATE).firstname("  firstname   ").lastname(" \tlastname\r\n").niss("***********").id(123).build();
        assertThat(req.getFirstname()).isEqualTo("firstname");
        assertThat(req.getLastname()).isEqualTo("lastname");

        assertThat(req.toRecord()).isEqualTo("00010000000000000000123***********0000LASTNAME,FIRSTNAME            ");
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
        # PaymentType,       Expected
        NULL,                ' '
        BANK_TRANSFER,       '1'
        OTHER_BANK_TRANSFER, '2'
        CIRCULAR_CHEQUE,     '3'
        """)
    void paymentTypeToRecord(PaymentType pm, char expected) {
        assertThat(PersonRequest.toRecord(pm)).isEqualTo(expected);
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", textBlock = """
        # Street,                   nbr,  box,       Expected
        Naamsestraat,               4,    NULL,      'NAAMSESTRAAT 4                '
        Boulevard Émile Jacqmain,   132,  bte a,     'BOULEVARD EMILE JACQ 132 BTE A'
        """)
    void formatAddress(String street, String nbr, String box, String expected) {
        Address address = Address.builder().street(street).number(nbr).box(box).build();
        assertThat(PersonRequest.formatAddress(address)).isEqualTo(expected);
    }

}