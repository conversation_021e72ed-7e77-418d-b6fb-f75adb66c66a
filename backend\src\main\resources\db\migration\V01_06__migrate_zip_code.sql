-- Update the new column with existing zip values (converted to string)
UPDATE PERSON_REQUEST SET zip_code = CAST(zip AS VARCHAR(10)) WHERE zip IS NOT NULL;

-- Drop the old zip column
ALTER TABLE PERSON_REQUEST DROP COLUMN zip;

-- Modify city column to support longer names (35 chars as per spec)
ALTER TABLE PERSON_REQUEST ALTER COLUMN city VARCHAR(35);

-- Rename the new column to zip
EXEC sp_rename 'dbo.PERSON_REQUEST.zip_code', 'zip', 'COLUMN';
