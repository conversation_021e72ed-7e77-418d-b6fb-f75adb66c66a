create table keybox_ds
(
    num_pens          decimal(10) not null
        constraint keybox_access
            primary key
                with (fillfactor = 50),
    nom_prenom        char(30),
    phoneme_nom       char(6),
    phoneme_prenom    char(4),
    num_box           int,
    sexe_clair        smallint,
    date_naiss        int,
    code_post         int,
    last_id           smallint,
    num_br            smallint,
    flag_id           bit         not null,
    flag_ok           bit         not null,
    flag_to_purge     bit         not null,
    flag_purge        bit         not null,
    flag_verifiable   bit         not null,
    flag_pp_onl       bit         not null,
    flag_keybox_1     bit         not null,
    flag_keybox_2     bit         not null,
    flag_keybox_3     bit         not null,
    flag_keybox_4     bit         not null,
    flag_keybox_5     bit         not null,
    flag_keybox_6     bit         not null,
    flag_keybox_7     bit         not null,
    flag_keybox_8     bit         not null,
    flag_keybox_9     bit         not null,
    flag_keybox_10    bit         not null,
    flag_ok_ic        bit         not null,
    flag_val_ic       bit         not null,
    flag_applic_1_kb  bit         not null,
    flag_applic_2_kb  bit         not null,
    flag_applic_3_kb  bit         not null,
    flag_applic_4_kb  bit         not null,
    flag_applic_5_kb  bit         not null,
    flag_applic_6_kb  bit         not null,
    flag_applic_7_kb  bit         not null,
    flag_applic_8_kb  bit         not null,
    flag_applic_9_kb  bit         not null,
    flag_applic_10_kb bit         not null,
    flag_c1           bit         not null,
    flag_personnel    bit         not null,
    source_id         tinyint     not null,
    my_id             int identity
)

create unique index keybox_ds_tk
    on keybox_ds (my_id)
    with (fillfactor = 50)

create index keybox_ds_fk
    on keybox_ds (num_pens)
    with (fillfactor = 50)

create index Keybox_ds_NumBox
    on keybox_ds (num_box, last_id)
    with (fillfactor = 50)

create index NC__keybox_ds__last_id__Include
    on keybox_ds (last_id) include (num_pens, num_box, nom_prenom, sexe_clair, code_post, num_br)

create index NC__keybox_ds__code_post__Include
    on keybox_ds (code_post) include (num_pens, num_br)
    with (fillfactor = 50)

create index NC__keybox_ds__last_id__num_box__Include
    on keybox_ds (last_id, num_box) include (num_pens, nom_prenom)
    with (fillfactor = 50)

create index NC__keybox_ds__num_br__num_pens
    on keybox_ds (num_br, num_pens)
    with (fillfactor = 50)

-- auto-generated definition
create table bar_ds
(
    my_aa             char(12) not null
        constraint aa_set_bar_ds
            primary key,
    bar1              char(9),
    bar2              char(9),
    bar3              char(9),
    bar_art_adm1      char(11),
    bar_art_adm2      char(11),
    bar_art_adm3      char(11),
    bar_art1          char(6),
    bar_art2          char(6),
    bar_art3          char(6),
    num_box           int,
    bar_dat_deb1      int,
    flag_valid        smallint,
    date_modif        int,
    date_modif_f4     int,
    code_operateur    int,
    code_operateur_f4 int,
    bar_dat_deb2      int,
    bar_dat_deb3      int,
    bar_dat_ech       int,
    bar_fact_q_x100   int,
    bar_fact_q_x100_b int,
    bar_fact_q_x100_t int,
    montant_jour_01   int,
    montant_jour_02   int,
    montant_jour_03   int,
    montant_jour_04   int,
    montant_jour_05   int,
    montant_jour_06   int,
    montant_jour_07   int,
    montant_jour_08   int,
    montant_jour_09   int,
    montant_jour_10   int,
    montant_jour_11   int,
    montant_jour_12   int,
    bar_compl_code    smallint,
    bar_compl_mt      int,
    c1_prem           int,
    flag_verif_util   bit      not null,
    bar_comment       char(40),
    flag_stat11       bit      not null,
    bar_q_x100        int,
    bar_s_x100        int,
    bar_jva           char(4),
    bar_jva_deb       int,
    bar_jva_mt        int,
    dat_ech_compl     int,
    pwa_uren          smallint,
    pwa_bedrag        decimal(4, 2),
    bar4              char(9),
    bar_dat_deb4      int,
    time_modif        int,
    bar_01            char(9),
    bar_02            char(9),
    bar_03            char(9),
    bar_04            char(9),
    bar_05            char(9),
    bar_06            char(9),
    bar_07            char(9),
    bar_08            char(9),
    bar_09            char(9),
    bar_10            char(9),
    bar_11            char(9),
    bar_12            char(9),
    bar_dat_deb_01    int,
    bar_dat_deb_02    int,
    bar_dat_deb_03    int,
    bar_dat_deb_04    int,
    bar_dat_deb_05    int,
    bar_dat_deb_06    int,
    bar_dat_deb_07    int,
    bar_dat_deb_08    int,
    bar_dat_deb_09    int,
    bar_dat_deb_10    int,
    bar_dat_deb_11    int,
    bar_dat_deb_12    int,
    contexte          char(2),
    nbr_mois          smallint,
    a_partir_de       int,
    p_prof_d          smallint,
    p_prof_an         smallint,
    p_prof_b          smallint,
    p_prof_d_j        int,
    p_prof_an_j       int,
    p_prof_b_j        int,
    calc_jusque       int,
    reg_specif        char(2),
    bar_art4          char(6),
    bar_art5          char(6),
    bar_art6          char(6),
    nbr_jours         smallint,
    type_c9           char(3),
    num_op_a          char(11),
    num_br            smallint,
    sect_op           int,
    solde_p2          smallint,
    ajout_pp          smallint,
    date_deb_b        int,
    date_fin_b        int,
    date_deb_anp      int,
    date_fin_anp      int,
    jours_a           smallint,
    jours_b           smallint,
    flag_modif_l710   bit      not null,
    niss_parent       int,
    attest_nr         char(15),
    gewest_nr         smallint,
    pays_parent       tinyint,
    nom_parent        char(25),
    deb_cascade       int,
    autre_l710        char,
    new_pertinent     char,
    source_id         tinyint  not null,
    my_id             int identity
)

create unique index bar_ds_tk
    on bar_ds (my_id)

create index bar_ds_fk
    on bar_ds (num_box, source_id)

create index NC__bar_ds__flag_valid__bar_01__Include
    on bar_ds (flag_valid, bar_01) include (num_box, bar_dat_deb1, bar_dat_deb_01)

create index NC__bar_ds__flag_valid__Include
    on bar_ds (flag_valid) include (bar1, bar2, bar3, num_box, bar_dat_deb1, bar_dat_deb2, bar_dat_deb3)
    with (fillfactor = 50)

create index NC__dbo_bar_ds__flag_valid__num_box__bar_dat_deb1__Include
    on bar_ds (flag_valid, num_box, bar_dat_deb1) include (my_aa)

create index NC__dbo_bar_ds__num_box__flag_valid__source_id__Include
    on bar_ds (num_box, flag_valid, source_id) include (my_aa)

-- auto-generated definition
create table sanct_ds
(
    my_aa             char(12) not null
        constraint aa_set_sanct_ds
            primary key
                with (fillfactor = 50),
    sanct_s_art       char(9),
    num_box           int,
    date_valid        int,
    flag_valid        smallint,
    date_modif        int,
    code_operateur    int,
    sanct_duree       smallint,
    sanct_debut       int,
    c1_prem           int,
    flag_verif_util   bit      not null,
    flag_stat11       bit      not null,
    date_modif_f4     int,
    code_operateur_f4 int,
    sanct_fin         int,
    sanct_dur_sursis  smallint,
    sanct_deb_sursis  int,
    time_modif        int,
    attest_nr         char(15),
    gewest_nr         smallint,
    source_id         tinyint  not null,
    my_id             int identity
)

create unique index sanct_ds_tk
    on sanct_ds (my_id)
    with (fillfactor = 50)

create index sanct_ds_fk
    on sanct_ds (num_box, source_id)
    with (fillfactor = 50)

SET IDENTITY_INSERT keybox_ds ON
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_verifiable, flag_pp_onl, flag_keybox_1, flag_keybox_2, flag_keybox_3, flag_keybox_4, flag_keybox_5, flag_keybox_6, flag_keybox_7, flag_keybox_8, flag_keybox_9, flag_keybox_10, flag_ok_ic, flag_val_ic, flag_applic_1_kb, flag_applic_2_kb, flag_applic_3_kb, flag_applic_4_kb, flag_applic_5_kb, flag_applic_6_kb, flag_applic_7_kb, flag_applic_8_kb, flag_applic_9_kb, flag_applic_10_kb, flag_c1, flag_personnel, source_id, my_id) VALUES (188117117, N'LASTNAME2,NAME2       ', N'LNAM2  ', N'NAM2', 48640, 1, 188117, 2235, 9, 13, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18338);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_verifiable, flag_pp_onl, flag_keybox_1, flag_keybox_2, flag_keybox_3, flag_keybox_4, flag_keybox_5, flag_keybox_6, flag_keybox_7, flag_keybox_8, flag_keybox_9, flag_keybox_10, flag_ok_ic, flag_val_ic, flag_applic_1_kb, flag_applic_2_kb, flag_applic_3_kb, flag_applic_4_kb, flag_applic_5_kb, flag_applic_6_kb, flag_applic_7_kb, flag_applic_8_kb, flag_applic_9_kb, flag_applic_10_kb, flag_c1, flag_personnel, source_id, my_id) VALUES (999213078, N'LASTNAME3,NAME3          ', N'LNAM3 ', N'NAM3', 13, 2, 188513, 8620, 9, 35, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27113);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_verifiable, flag_pp_onl, flag_keybox_1, flag_keybox_2, flag_keybox_3, flag_keybox_4, flag_keybox_5, flag_keybox_6, flag_keybox_7, flag_keybox_8, flag_keybox_9, flag_keybox_10, flag_ok_ic, flag_val_ic, flag_applic_1_kb, flag_applic_2_kb, flag_applic_3_kb, flag_applic_4_kb, flag_applic_5_kb, flag_applic_6_kb, flag_applic_7_kb, flag_applic_8_kb, flag_applic_9_kb, flag_applic_10_kb, flag_c1, flag_personnel, source_id, my_id) VALUES (999219079, N'LASTNAME4,NAME4              ', N'LNAM4 ', N'NAM4',  15, 1, 189219, 5171, 9, 92, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2930);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_verifiable, flag_pp_onl, flag_keybox_1, flag_keybox_2, flag_keybox_3, flag_keybox_4, flag_keybox_5, flag_keybox_6, flag_keybox_7, flag_keybox_8, flag_keybox_9, flag_keybox_10, flag_ok_ic, flag_val_ic, flag_applic_1_kb, flag_applic_2_kb, flag_applic_3_kb, flag_applic_4_kb, flag_applic_5_kb, flag_applic_6_kb, flag_applic_7_kb, flag_applic_8_kb, flag_applic_9_kb, flag_applic_10_kb, flag_c1, flag_personnel, source_id, my_id) VALUES (998624049, N'LASTNAME5,NAME5                 ', N'LNAM5', N'NAM5', 38448, 1, 198624, 7270, 9, 53, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18984);
INSERT INTO keybox_ds (num_pens, nom_prenom, phoneme_nom, phoneme_prenom, num_box, sexe_clair, date_naiss, code_post, last_id, num_br, flag_id, flag_ok, flag_to_purge, flag_purge, flag_verifiable, flag_pp_onl, flag_keybox_1, flag_keybox_2, flag_keybox_3, flag_keybox_4, flag_keybox_5, flag_keybox_6, flag_keybox_7, flag_keybox_8, flag_keybox_9, flag_keybox_10, flag_ok_ic, flag_val_ic, flag_applic_1_kb, flag_applic_2_kb, flag_applic_3_kb, flag_applic_4_kb, flag_applic_5_kb, flag_applic_6_kb, flag_applic_7_kb, flag_applic_8_kb, flag_applic_9_kb, flag_applic_10_kb, flag_c1, flag_personnel, source_id, my_id) VALUES (889231808, N'LASTNAME1,NAME1             ', N'LNAM1', N'NAM1',991, 1, 418001, 1300, 9, 23, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1376);

SET IDENTITY_INSERT keybox_ds OFF

create table general_ds
(
    my_aa             char(12) not null
        constraint aa_set_general_ds
            primary key,
    nom_prenom_gn     char(30),
    adresse           char(30),
    num_box           int,
    code_post         int,
    langue_gn smallint,
    sexe_gn smallint,
    flag_nation bit,
    sect_op int,
    num_br smallint,
    cpte_iban_gn char(34),
    date_deces_car_gn int,
    flag_v_cpte bit
)
    go

create index NC__dbo_general_ds__nom_prenom_gn__num_box
    on general_ds (nom_prenom_gn, num_box)
    go



SET IDENTITY_INSERT sanct_ds ON

INSERT INTO general_ds (my_aa, num_box, nom_prenom_gn, adresse, code_post, sect_op, num_br, cpte_iban_gn, date_deces_car_gn,flag_v_cpte) VALUES (N'000000000534', 48640, N'LASTNAME2,NAME2 ', N'ADRESS1,28               ', 48640,2350,33,'BE324836485274',99999999,0);

SET IDENTITY_INSERT sanct_ds OFF

create table communic_ds
(
    num_box            int      not null,
    date_valid         int      not null,
    flag_valid         smallint not null,
    date_modif         int,
    code_operateur     int,
    date_modif_f4      int,
    code_operateur_f4  int,
    tel_reg            char(17),
    gsm_reg            char(17),
    email_reg          char(50),
    tel_onem           char(17),
    gsm_onem           char(17),
    email_onem         char(50),
    date_modif_reg     int,
    date_modif_onem    int,
    e_box              char,
    date_modif_e_box   int,
    e_box_active       char,
    paperless          char,
    email_onem_2       char(50),
    source_id          tinyint  not null,
    tel_reg_sanitized  varchar(20),
    gsm_reg_sanitized  varchar(20),
    tel_onem_sanitized varchar(20),
    gsm_onem_sanitized varchar(20),
    id                 bigint identity
        constraint PK_dbo_communic_dsid
            primary key,
    my_id              int,
    my_aa              char(12)
)


create unique index aa_set_communic_ds
    on communic_ds (my_aa)


create index communic_by_box
    on communic_ds (num_box asc, date_valid desc, flag_valid desc, id desc)


create index communic_ds_fk
    on communic_ds (num_box, source_id)


create unique index communic_ds_tk
    on communic_ds (my_id)


create index NC__dbo_communic_ds__flag_valid__gsm_reg_sanitized__Include
    on communic_ds (flag_valid, gsm_reg_sanitized) include (num_box, tel_reg_sanitized, email_reg, tel_onem_sanitized,
                                                            gsm_onem_sanitized, email_onem, my_aa, my_id)


create index NC__dbo_communic_ds__flag_valid__Include
    on communic_ds (flag_valid) include (num_box, tel_reg, gsm_reg, email_reg, tel_onem, gsm_onem, email_onem)


create index NC__dbo_Communic_ds__flag_valid__num_box__Include
    on communic_ds (flag_valid, num_box) include (my_aa, email_reg, email_onem)


create table dbo.nation_ds
(
    num_box           int      not null,
    code_operateur    int,
    flag_valid        smallint not null,
    date_valid        int      not null,
    date_modif        int,
    nation            smallint,
    date_modif_f4     int,
    code_operateur_f4 int,
    nation_bcss       smallint,
    refugie           smallint,
    source_id         tinyint  not null,
    id                bigint identity
        constraint PK_dbo_nation_dsid
            primary key,
    my_id             int,
    my_aa             char(12)
)


create unique index aa_set_nation_ds
    on dbo.nation_ds (my_aa)


create unique index nation_by_9
    on dbo.nation_ds (num_box asc, date_valid desc) include (flag_valid)


create index nation_by_9_nu
    on dbo.nation_ds (num_box asc, date_valid desc) include (flag_valid)


create index nation_by_box
    on dbo.nation_ds (num_box asc, date_valid desc, flag_valid desc, id desc)


create index NC__nation_ds__flag_valid__num_box__date_valid
    on dbo.nation_ds (flag_valid, num_box, date_valid)


create index NC__nation_ds__num_box__flag_valid__date_valid
    on dbo.nation_ds (num_box, flag_valid, date_valid)


create unique index UDX__nation_ds__my_id
    on dbo.nation_ds (my_id)

create table dbo.cpte_ds
(
    cpte_tit          char(20),
    date_valid        int      not null,
    flag_valid        smallint not null,
    date_modif        int,
    date_modif_f4     int,
    code_operateur    int,
    code_operateur_f4 int,
    cpte_num          decimal(11),
    cpte_iban         char(34),
    cpte_bic          char(11),
    source_id         tinyint  not null,
    user_column1      tinyint,
    id                bigint identity
        constraint PK_dbo_cpte_dsid
            primary key,
    my_id             int,
    my_rsn            char(12),
    parent_aa         char(12)
)

create unique index aa_set_cpte_ds
    on dbo.cpte_ds (my_rsn)

create index cpte_access
    on dbo.cpte_ds ( date_valid desc, flag_valid desc, id desc)

create unique index PARENT_cpte_ds
    on dbo.cpte_ds ( id)
SET IDENTITY_INSERT dbo.cpte_ds ON;
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_aa)VALUES (N'                    ', 20040629, 9, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 8, 2, N'8           ', N'000000000534          ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_aa)VALUES (N'                    ', 20040629, 9, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 8, 2, N'8           ', N'2          ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_aa)VALUES (N'                    ', 20200629, 9, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 8, 2, N'8           ', N'2          ');
INSERT INTO dbo.cpte_ds (cpte_tit, date_valid, flag_valid, date_modif, date_modif_f4, code_operateur, code_operateur_f4, cpte_num, cpte_iban, cpte_bic, source_id, user_column1, id, my_id, my_rsn, parent_aa)VALUES (N'                    ', 20040629, 1, 20090206, 20090206, 0, 0, 3300121083, N'****************                  ', N'BBRUBEBB   ', 1, 0, 8, 2, N'8           ', N'3          ');
SET IDENTITY_INSERT dbo.cpte_ds OFF;



create table dbo.signal_ic
(
    num_box           int      not null,
    date_valid        int      not null,
    flag_valid        smallint not null,
    date_modif        int      not null,
    code_operateur    int,
    date_modif_f4     int,
    code_operateur_f4 int,
    num_br            smallint,
    nation            smallint,
    etat_civil        smallint,
    adresse           char(30),
    code_post         int,
    code_resid        int,
    nbr_enfant        smallint,
    date_naiss_cadet  int,
    tp_preced_x100_1  int,
    tp_preced_x100_2  int,
    age_pension       smallint,
    contrat_ic        smallint,
    prof_ic           smallint,
    alloc_fam_ic_1    smallint,
    alloc_fam_ic_2    smallint,
    flag_sig_ic_1     bit      not null,
    flag_sig_ic_2     bit      not null,
    flag_sig_ic_3     bit      not null,
    flag_sig_ic_4     bit      not null,
    flag_sig_ic_5     bit      not null,
    flag_sig_ic_6     bit      not null,
    flag_sig_ic_7     bit      not null,
    flag_sig_ic_8     bit      not null,
    flag_sig_ic_9     bit      not null,
    flag_sig_ic_10    bit      not null,
    date_sig_ic_1     int,
    date_sig_ic_2     int,
    date_sig_ic_3     int,
    adr_etranger_1    char(40),
    adr_etranger_2    char(40),
    precompte         char,
    sit_familiale     char,
    langue            smallint,
    nation_bcss       smallint,
    refugie           smallint,
    hors_eee_aut      char,
    ticket_no         char(13),
    origin            char(2),
    indic_adr_r300    bit      not null,
    analogic_c30      char(17),
    source_id         tinyint  not null,
    id                bigint identity
        constraint PK_dbo_signal_icid
            primary key,
    my_id             int,
    my_aa             char(12)
)
go

create unique index aa_set_signal_ic
    on dbo.signal_ic (my_aa)
go

create unique index signal_ic_by_9
    on dbo.signal_ic (num_box asc, date_valid desc) include (flag_valid)
go

create index signal_ic_by_9_nu
    on dbo.signal_ic (num_box asc, date_valid desc)
go

create index signal_ic_by_box
    on dbo.signal_ic (num_box asc, date_valid desc, flag_valid desc, id desc)
go

create index signal_ic_by_date
    on dbo.signal_ic (num_box asc, date_valid desc, date_modif desc, flag_valid desc, id desc) include (date_modif_f4, tp_preced_x100_2)
go

create index signal_ic_fk
    on dbo.signal_ic (num_box, source_id)
go

create unique index signal_ic_tk
    on dbo.signal_ic (my_id)
go
-- SET IDENTITY_INSERT signal_ic ON
--
-- INSERT INTO dbo.signal_ic (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4, num_br, nation, etat_civil, adresse, code_post, code_resid, nbr_enfant, date_naiss_cadet, tp_preced_x100_1, tp_preced_x100_2, age_pension, contrat_ic, prof_ic, alloc_fam_ic_1, alloc_fam_ic_2, flag_sig_ic_1, flag_sig_ic_2, flag_sig_ic_3, flag_sig_ic_4, flag_sig_ic_5, flag_sig_ic_6, flag_sig_ic_7, flag_sig_ic_8, flag_sig_ic_9, flag_sig_ic_10, date_sig_ic_1, date_sig_ic_2, date_sig_ic_3, adr_etranger_1, adr_etranger_2, precompte, sit_familiale, langue, nation_bcss, refugie, hors_eee_aut, ticket_no, origin, indic_adr_r300, analogic_c30, source_id, id, my_id, my_aa) VALUES (48640, 20030801, 9, 20051230, 3014, 20111206, 0, 21, 1, 0, N'RUE GROESELENBERG,37/21       ', 1180, 21016, 0, 0, 3750, 3750, 0, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, N'                                        ', N'                                        ', N' ', N' ', 0, 0, 0, N'N', N'             ', N'  ', 0, N'                 ', 1, 1, 1, N'1           ');
-- INSERT INTO dbo.signal_ic (num_box, date_valid, flag_valid, date_modif, code_operateur, date_modif_f4, code_operateur_f4, num_br, nation, etat_civil, adresse, code_post, code_resid, nbr_enfant, date_naiss_cadet, tp_preced_x100_1, tp_preced_x100_2, age_pension, contrat_ic, prof_ic, alloc_fam_ic_1, alloc_fam_ic_2, flag_sig_ic_1, flag_sig_ic_2, flag_sig_ic_3, flag_sig_ic_4, flag_sig_ic_5, flag_sig_ic_6, flag_sig_ic_7, flag_sig_ic_8, flag_sig_ic_9, flag_sig_ic_10, date_sig_ic_1, date_sig_ic_2, date_sig_ic_3, adr_etranger_1, adr_etranger_2, precompte, sit_familiale, langue, nation_bcss, refugie, hors_eee_aut, ticket_no, origin, indic_adr_r300, analogic_c30, source_id, id, my_id, my_aa) VALUES (48640, 20030801, 1, 20030711, 3657, 20051230, 3014, 21, 1, 0, N'RUE GROESELENBERG,37/21       ', 1180, 21016, 0, 0, 3750, 3750, 0, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, N'                                        ', N'                                        ', N' ', N' ', 0, 0, 0, N'N', N'             ', N'  ', 0, N'                 ', 1, 2, 2, N'2           ');
--
-- SET IDENTITY_INSERT signal_ic OFF

create table dbo.general_ic
(
    num_box           int         not null
        constraint UK_dbo_general_icgeneral_ic_by_box
            unique,
    num_br            smallint    not null,
    num_pens          decimal(10) not null,
    nom_prenom        char(30)    not null,
    sexe_clair        smallint,
    langue            smallint,
    date_modif        int,
    code_operateur    int,
    flag_present_i_1  bit         not null,
    flag_present_i_2  bit         not null,
    flag_present_i_3  bit         not null,
    flag_present_i_4  bit         not null,
    flag_present_i_5  bit         not null,
    flag_present_i_6  bit         not null,
    flag_present_i_7  bit         not null,
    flag_present_i_8  bit         not null,
    flag_present_i_9  bit         not null,
    flag_present_i_10 bit         not null,
    flag_present_i_11 bit         not null,
    flag_present_i_12 bit         not null,
    flag_present_i_13 bit         not null,
    flag_present_i_14 bit         not null,
    flag_present_i_15 bit         not null,
    flag_present_i_16 bit         not null,
    flag_present_i_17 bit         not null,
    flag_present_i_18 bit         not null,
    flag_present_i_19 bit         not null,
    flag_present_i_20 bit         not null,
    flag_present_i_21 bit         not null,
    flag_present_i_22 bit         not null,
    flag_present_i_23 bit         not null,
    flag_present_i_24 bit         not null,
    flag_present_i_25 bit         not null,
    flag_present_i_26 bit         not null,
    flag_present_i_27 bit         not null,
    flag_present_i_28 bit         not null,
    flag_present_i_29 bit         not null,
    flag_present_i_30 bit         not null,
    flag_present_i_31 bit         not null,
    flag_present_i_32 bit         not null,
    flag_present_i_33 bit         not null,
    flag_present_i_34 bit         not null,
    flag_present_i_35 bit         not null,
    flag_present_i_36 bit         not null,
    flag_present_i_37 bit         not null,
    flag_present_i_38 bit         not null,
    flag_present_i_39 bit         not null,
    flag_present_i_40 bit         not null,
    flag_present_i_41 bit         not null,
    flag_present_i_42 bit         not null,
    flag_present_i_43 bit         not null,
    flag_present_i_44 bit         not null,
    flag_present_i_45 bit         not null,
    flag_present_i_46 bit         not null,
    flag_present_i_47 bit         not null,
    flag_present_i_48 bit         not null,
    flag_payable      smallint,
    date_payable      int         not null,
    code_operateur_p  int,
    date_recalcul     int,
    flag_gen_ic_1     bit         not null,
    flag_gen_ic_2     bit         not null,
    flag_gen_ic_3     bit         not null,
    flag_gen_ic_4     bit         not null,
    flag_gen_ic_5     bit         not null,
    flag_gen_ic_6     bit         not null,
    flag_gen_ic_7     bit         not null,
    flag_gen_ic_8     bit         not null,
    flag_gen_ic_9     bit         not null,
    flag_gen_ic_10    bit         not null,
    date_gen_ic_1     int,
    date_gen_ic_2     int,
    date_gen_ic_3     int,
    date_gen_ic_4     int,
    date_gen_ic_5     int,
    date_gen_ic_6     int,
    mois_gen_ic_1     int,
    mois_gen_ic_2     int,
    credit_ic_ibm_01  int,
    credit_ic_ibm_02  int,
    credit_ic_ibm_03  int,
    credit_ic_ibm_04  int,
    credit_ic_ibm_05  int,
    credit_ic_ibm_06  int,
    credit_ic_ibm_07  int,
    credit_ic_ibm_08  int,
    credit_ic_ibm_09  int,
    credit_ic_ibm_10  int,
    credit_ic_sys_01  int,
    credit_ic_sys_02  int,
    credit_ic_sys_03  int,
    credit_ic_sys_04  int,
    credit_ic_sys_05  int,
    credit_ic_sys_06  int,
    credit_ic_sys_07  int,
    credit_ic_sys_08  int,
    credit_ic_sys_09  int,
    credit_ic_sys_10  int,
    flag_statut_cor   tinyint,
    date_deces_carr   int,
    date_gn_deces_car int,
    etat_civ_carr     smallint,
    upd_etciv_carr    int,
    etciv_carr_v_date int,
    type_statut       tinyint     not null,
    ic_bits_01        smallint,
    ic_bits_02        smallint,
    ic_bits_03        smallint,
    ic_bits_04        smallint,
    ic_bits_05        smallint,
    ic_bits_06        smallint,
    ic_bits_07        smallint,
    ic_bits_08        smallint,
    ic_bits_09        smallint,
    ic_bits_10        smallint,
    ic_bits_11        smallint,
    ic_bits_12        smallint,
    ic_bits_13        smallint,
    ic_bits_14        smallint,
    ic_bits_15        smallint,
    ic_bits_16        smallint,
    ic_bits_17        smallint,
    ic_bits_18        smallint,
    ic_bits_19        smallint,
    ic_bits_20        smallint,
    ic_bits_21        smallint,
    ic_bits_22        smallint,
    ic_bits_23        smallint,
    ic_bits_24        smallint,
    ic_bits_25        smallint,
    ic_bits_26        smallint,
    ic_bits_27        smallint,
    ic_bits_28        smallint,
    ic_bits_29        smallint,
    ic_bits_30        smallint,
    ic_bits_31        smallint,
    ic_bits_32        smallint,
    ic_bits_33        smallint,
    ic_bits_34        smallint,
    ic_bits_35        smallint,
    ic_bits_36        smallint,
    ic_bits_37        smallint,
    ic_bits_38        smallint,
    ic_bits_39        smallint,
    ic_bits_40        smallint,
    ic_bits_41        smallint,
    ic_bits_42        smallint,
    ic_bits_43        smallint,
    ic_bits_44        smallint,
    ic_bits_45        smallint,
    ic_bits_46        smallint,
    ic_bits_47        smallint,
    ic_bits_48        smallint,
    ic_bits_49        smallint,
    ic_bits_50        smallint,
    ic_bits_51        smallint,
    ic_bits_52        smallint,
    ic_bits_maand1    int,
    last_credit       smallint,
    email             char(50),
    archive_scanned   tinyint     not null,
    etudes_ic         char(6),
    flag_calc_pro     bit         not null,
    analogic_active   char,
    controle_ct       char,
    source_id         tinyint     not null,
    id                bigint identity
        constraint PK_dbo_general_icid
            primary key,
    my_id             int,
    constraint UK_dbo_general_icgeneral_ic_by_br
        unique (num_br, num_pens)
)
go

create unique index gen_ic_arch2scan
    on dbo.general_ic (num_br, archive_scanned, nom_prenom, num_box)
go

create index gen_ic_arch2scan_nu
    on dbo.general_ic (num_br, archive_scanned, nom_prenom, num_box)
go

create unique index gen_ic_calc_pro
    on dbo.general_ic (num_box) include (flag_calc_pro)
go

create index gen_ic_calc_pro_nu
    on dbo.general_ic (num_box) include (flag_calc_pro)
go

create unique index general_ic_by_box
    on dbo.general_ic (num_box)
go

create unique index general_ic_by_br
    on dbo.general_ic (num_br, num_pens)
go

create unique index general_ic_by_cor
    on dbo.general_ic (num_br, num_pens) include (flag_statut_cor)
go

create index general_ic_by_cor_nu
    on dbo.general_ic (num_br, num_pens) include (flag_statut_cor)
go

create unique index general_ic_not_p
    on dbo.general_ic (num_br asc, date_payable asc, type_statut desc, num_pens asc) include (flag_payable)
go

create index general_ic_not_p_nu
    on dbo.general_ic (num_br asc, date_payable asc, type_statut desc, num_pens asc) include (flag_payable)
go

create unique index general_ic_tk
    on dbo.general_ic (my_id)
go

create index NC__dbo_general_ic__nom_prenom__num_box
    on dbo.general_ic (nom_prenom, num_box)
go

create index NC__general_ic__num_pens
    on dbo.general_ic (num_pens)
go
SET IDENTITY_INSERT general_ic ON

INSERT INTO dbo.general_ic (num_box, num_br, num_pens, nom_prenom, sexe_clair, langue, date_modif, code_operateur, flag_present_i_1, flag_present_i_2, flag_present_i_3, flag_present_i_4, flag_present_i_5, flag_present_i_6, flag_present_i_7, flag_present_i_8, flag_present_i_9, flag_present_i_10, flag_present_i_11, flag_present_i_12, flag_present_i_13, flag_present_i_14, flag_present_i_15, flag_present_i_16, flag_present_i_17, flag_present_i_18, flag_present_i_19, flag_present_i_20, flag_present_i_21, flag_present_i_22, flag_present_i_23, flag_present_i_24, flag_present_i_25, flag_present_i_26, flag_present_i_27, flag_present_i_28, flag_present_i_29, flag_present_i_30, flag_present_i_31, flag_present_i_32, flag_present_i_33, flag_present_i_34, flag_present_i_35, flag_present_i_36, flag_present_i_37, flag_present_i_38, flag_present_i_39, flag_present_i_40, flag_present_i_41, flag_present_i_42, flag_present_i_43, flag_present_i_44, flag_present_i_45, flag_present_i_46, flag_present_i_47, flag_present_i_48, flag_payable, date_payable, code_operateur_p, date_recalcul, flag_gen_ic_1, flag_gen_ic_2, flag_gen_ic_3, flag_gen_ic_4, flag_gen_ic_5, flag_gen_ic_6, flag_gen_ic_7, flag_gen_ic_8, flag_gen_ic_9, flag_gen_ic_10, date_gen_ic_1, date_gen_ic_2, date_gen_ic_3, date_gen_ic_4, date_gen_ic_5, date_gen_ic_6, mois_gen_ic_1, mois_gen_ic_2, credit_ic_ibm_01, credit_ic_ibm_02, credit_ic_ibm_03, credit_ic_ibm_04, credit_ic_ibm_05, credit_ic_ibm_06, credit_ic_ibm_07, credit_ic_ibm_08, credit_ic_ibm_09, credit_ic_ibm_10, credit_ic_sys_01, credit_ic_sys_02, credit_ic_sys_03, credit_ic_sys_04, credit_ic_sys_05, credit_ic_sys_06, credit_ic_sys_07, credit_ic_sys_08, credit_ic_sys_09, credit_ic_sys_10, flag_statut_cor, date_deces_carr, date_gn_deces_car, etat_civ_carr, upd_etciv_carr, etciv_carr_v_date, type_statut, ic_bits_01, ic_bits_02, ic_bits_03, ic_bits_04, ic_bits_05, ic_bits_06, ic_bits_07, ic_bits_08, ic_bits_09, ic_bits_10, ic_bits_11, ic_bits_12, ic_bits_13, ic_bits_14, ic_bits_15, ic_bits_16, ic_bits_17, ic_bits_18, ic_bits_19, ic_bits_20, ic_bits_21, ic_bits_22, ic_bits_23, ic_bits_24, ic_bits_25, ic_bits_26, ic_bits_27, ic_bits_28, ic_bits_29, ic_bits_30, ic_bits_31, ic_bits_32, ic_bits_33, ic_bits_34, ic_bits_35, ic_bits_36, ic_bits_37, ic_bits_38, ic_bits_39, ic_bits_40, ic_bits_41, ic_bits_42, ic_bits_43, ic_bits_44, ic_bits_45, ic_bits_46, ic_bits_47, ic_bits_48, ic_bits_49, ic_bits_50, ic_bits_51, ic_bits_52, ic_bits_maand1, last_credit, email, archive_scanned, etudes_ic, flag_calc_pro, analogic_active, controle_ct, source_id, id, my_id) VALUES (11390, 21, 568918047, N'DUTRY,RUDY                    ', 1, 1, 20051230, 3014, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 20060110, 3818, 99999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 99999999, 20030801, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 99999999, 0, 40, 20030719, 19931116, 1, 4095, 4095, 4095, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 514, 32, 200607, 0, N'                                                  ', 9, N'      ', 0, N' ', N' ', 1, 139, 139);
SET IDENTITY_INSERT general_ic OFF

create table communic_ic
(
    num_box           int      not null,
    date_valid        int      not null,
    flag_valid        smallint not null,
    date_modif        int,
    code_operateur    int,
    date_modif_f4     int,
    code_operateur_f4 int,
    num_tel           char(17),
    num_gsm           char(17),
    adr_email         char(50),
    ident_etr_pays    smallint,
    ident_etr         char(20),
    e_box             char,
    date_modif_e_box  int,
    e_box_active      char,
    paperless         char,
    adr_email_2       char(50),
    source_id         tinyint  not null,
    num_gsm_sanitized varchar(20),
    num_tel_sanitized varchar(20),
    id                bigint identity
        constraint PK_dbo_communic_icid
            primary key,
    my_id             int,
    my_aa             char(12)
)

create unique index aa_set_communic_ic
    on communic_ic (my_aa)

create index com_ic_by_box
    on communic_ic (num_box asc, date_valid desc, flag_valid desc, id desc)

create index NC__communic_ic__num_box__flag_valid__source_id
    on communic_ic (num_box, flag_valid, source_id)

create index NC__dbo_communic_ic__flag_valid__num_gsm_sanitized__Include
    on communic_ic (flag_valid, num_gsm_sanitized) include (num_box, num_tel_sanitized, adr_email, adr_email_2, my_aa, my_id)


create table cpte_ic
(
    num_box           int         not null,
    flag_valid        smallint    not null,
    date_valid        int         not null,
    date_modif        int,
    code_operateur    int,
    code_operateur_f4 int,
    date_modif_f4     int,
    cpte_num          decimal(10) not null,
    cpte_tit          char(40),
    double_cpte       char,
    cpte_iban         char(34)    not null,
    cpte_bic          char(11),
    cheque_circ       char,
    ticket_no         char(13),
    origin            char(2),
    source_id         tinyint     not null,
    id                bigint identity
        constraint PK_dbo_cpte_icid
            primary key,
    my_id             int,
    my_aa             char(12)
)

create unique index aa_set_cpte_ic
    on cpte_ic (my_aa)

create index cpte_ic_by_box
    on cpte_ic (num_box asc, date_valid desc, flag_valid desc, id desc)

create index cpte_ic_by_iban
    on cpte_ic (cpte_iban asc, num_box asc, date_valid desc, id desc) include (flag_valid)

create index cpte_ic_by_iban_nu
    on cpte_ic (cpte_iban asc, num_box asc, date_valid desc, id desc) include (flag_valid)

create index cpte_ic_by_num
    on cpte_ic (cpte_num asc, num_box asc, date_valid desc, id desc) include (flag_valid)

create index cpte_ic_by_num_nu
    on cpte_ic (cpte_num asc, num_box asc, date_valid desc, id desc) include (flag_valid)

create index cpte_ic_fk
    on cpte_ic (num_box, source_id)

create unique index cpte_ic_tk
    on cpte_ic (my_id)

create index NC__cpte_ic__flag_valid__cpte_iban
    on cpte_ic (flag_valid, cpte_iban)


SET IDENTITY_INSERT cpte_ic ON

INSERT INTO cpte_ic (num_box, flag_valid, date_valid, date_modif, code_operateur, code_operateur_f4,
                             date_modif_f4, cpte_num, cpte_tit, double_cpte, cpte_iban, cpte_bic, cheque_circ,
                             ticket_no, origin, source_id, my_id, my_aa) VALUES
    (48640, 9, 20060123, 20060124, 63, 63, 0, 2930564441, N'                                        '
    , N' ', N'****************                  ', N'GEBABEBB   ', N' ', N'             '
    , N'  ', 1, 1, N'1           ');

SET IDENTITY_INSERT cpte_ic OFF

create table dbo.siggen_ds
(
    date_valid        int      not null,
    flag_valid        smallint not null,
    id                bigint identity
        constraint PK_dbo_siggen_dsid
            primary key,
    parent_id         bigint   not null
        constraint FK_dbo_siggen_dsgeneral_ds
            references general_ds
)

create unique index PARENT_siggen_ds
    on dbo.siggen_ds (parent_id, id)

create index siggen_access
    on dbo.siggen_ds (parent_id asc, date_valid desc, flag_valid desc, id desc)
