package be.fgov.onerva.person.backend.citizen.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

@Builder
@Getter
@ToString
public class CitizenCreationRequest {
    private boolean allowance;
    @NotNull
    private BusinessDomain domain;
    @NotNull
    @Pattern(regexp = "\\d{11}")
    private String niss;
    @NotBlank
    private String firstname;
    @NotBlank
    private String lastname;

    private String correlationId;
}
