import {JAVA_COMMON_PRESET, JavaFileGenerator} from "@asyncapi/modelina";
import {fromFile, Parser} from '@asyncapi/parser';

const generator = new JavaFileGenerator({
    presets: [
        {
            preset: JAVA_COMMON_PRESET,
            options: {
                equal: true,
                hashCode: true,
                classToString: false,
                marshalling: false
            }
        }
    ]
});


export async function generate(): Promise<void> {
    const parser = new Parser();
    const { document } = await fromFile(parser, '../api/person_async.yaml').parse();
    const outputFolder = './target/generated-sources/modelina/be/fgov/onerva/person/msg/v1';
    const modelGenerationOptions = {
        packageName: 'be.fgov.onerva.person.msg.v1'
    };
    if (document) {
        await generator.generateToFiles(
            document.json(),
            outputFolder,
            modelGenerationOptions
        );
    }

}
if (require.main === module) {
    generate();
}